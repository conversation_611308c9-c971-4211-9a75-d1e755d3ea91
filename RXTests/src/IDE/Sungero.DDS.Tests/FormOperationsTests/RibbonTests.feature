Feature: RibbonTests
Тесты для работы с лентой в форме
    
    @AfterMainOperationsTest
    Scenario Outline: Удаление кнопки с ленты
        Given Запустить DevelopmentStudio
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника
        And Удалить кнопку DeletingButtonAction с ленты
        When Сохранить изменения
        And В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\DeletingButtonTestGroup
        Then Проверить визуальные изменения для кнопки DeletingButtonName действия DeletingButtonAction после удаления кнопки на ленте
        And Проверить метаданные после удаления кнопки на ленте
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после удаления элемента с ленты

    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |

    @AfterMainOperationsTest
    Scenario Outline: Изменение кнопки на ленте
        Given Запустить DevelopmentStudio
        And Сохранить метаданные для кнопки Jump справочника <entityName> модуля <moduleName> перед выполнением теста
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника 
        And В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\JumpGroup\Jump
        And Для кнопки задать Имя JumpCh
        And В форме элемента на ленте задать Действие JumpActionCh
        When Сохранить изменения
        And В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\JumpGroup\JumpCh
        Then Проверить визуальные изменения для кнопки JumpCh действия JumpActionCh после изменения кнопки на ленте
        And Проверить метаданные после изменения кнопки на ленте
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после изменения кнопки на ленте

    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |

    @AfterMainOperationsTest
    Scenario Outline: Изменение видимости кнопки на ленте
        Given Запустить DevelopmentStudio
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника
        And В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\JumpGroup\Jump
        And Установить значение Отображать на ленте false
        When Сохранить изменения
        Then Проверить визуальные изменения для кнопки Jump действия JumpAction после изменения видимости кнопки
        And Проверить метаданные после изменения видимости кнопки на ленте
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после изменения кнопки на ленте

    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |

    @AfterMainOperationsTest
    Scenario Outline: Добавление кнопки из панели инструментов на ленту через DragAndDrop
        Given Запустить DevelopmentStudio
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника
        And Перейти во вкладку Панель элементов в форме
        And Из панели элементов перенести Кнопка на Ленту Справа от элемента JumpAction
        And Перейти во вкладку Дерево в форме
        And Для кнопки задать Имя OpenDocumentRead
        And В форме элемента на ленте задать Действие Read
        When Сохранить изменения
        And В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\JumpGroup\OpenDocumentRead
        Then Проверить визуальные изменения для кнопки OpenDocumentRead действия Read после создания новой кнопки
        And Проверить метаданные после создания кнопки на ленте
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после создания кнопки на ленте

    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |

    @AfterMainOperationsTest
    Scenario Outline: Добавление кнопки-меню из панели инструментов на ленту через DragAndDrop
        Given Запустить DevelopmentStudio
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника
        And Перейти во вкладку Панель элементов в форме
        And Из панели элементов перенести Кнопка-меню на Ленту Справа от элемента JumpAction
        And Перейти во вкладку Дерево в форме
        And Для кнопки-меню задать Имя ImportDocument
        And Для кнопки-меню задать Отображаемое имя Import
        And Для кнопки-меню задать Всплывающую подсказку Import file
        And Для кнопки-меню задать Описание Import a file into the document.
        And В форме элемента на ленте задать Размер Small
        And Перейти во вкладку Панель элементов в форме
        And Из панели элементов перенести Кнопка в меню элемента Import на ленте Сверху
        And Для кнопки задать Имя OpenDocumentRead
        And В форме элемента на ленте задать Действие Read
        When Сохранить изменения
        And Перейти во вкладку Дерево в форме
        And В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\JumpGroup\ImportDocument\OpenDocumentRead
        Then Проверить визуальные изменения для кнопки OpenDocumentRead действия Read после добавления кнопки в кнопку-меню
        And Проверить метаданные после добавления элемента в кнопку-меню
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после добавления кнопки в кнопку-меню через Drag and drop
        
    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |

    @AfterMainOperationsTest
    Scenario Outline: Добавление группы из панели инструментов на ленту через DragAndDrop
        Given Запустить DevelopmentStudio
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника
        And Перейти во вкладку Панель элементов в форме
        And Из панели элементов перенести Группа на Ленту Справа от элемента JumpGroup
        And Для группы задать Имя DocumentCommands
        And Для группы задать Отображаемое имя Document
        And Из панели элементов перенести Кнопка в группу на ленте Document
        And Для кнопки задать Имя OpenDocumentRead
        And В форме элемента на ленте задать Действие Read
        When Сохранить изменения
        And Перейти во вкладку Дерево в форме
        When В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\DocumentCommands
        Then Проверить визуальные изменения для группы DocumentCommands после добавления группы в ленту
        When В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\MainPage\DocumentCommands\OpenDocumentRead
        Then Проверить визуальные изменения для кнопки OpenDocumentRead действия Read после добавления кнопки в группу
        And Проверить метаданные после добавления элемента в группу
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после добавления группы и кнопки в группу через Drag and drop

    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |

    @AfterMainOperationsTest
    Scenario Outline: Добавление вкладки из панели инструментов на ленту, через DragAndDrop
        Given Запустить DevelopmentStudio
        And Открыть редактор справочника <entityName> из модуля <moduleName>
        And Открыть редактор формы справочника
        And Перейти во вкладку Панель элементов в форме
        And Из панели элементов перенести Вкладка на Панель вкладок Справа от элемента ПОИСК
        And Для вкладки задать Имя Reports
        And Для вкладки задать Отображаемое имя Reports
        And Из панели элементов перенести Группа на Ленту Справа от элемента Group
        And Для группы задать Имя DocumentCommands
        And Для группы задать Отображаемое имя Document
        And Из панели элементов перенести Кнопка в группу на ленте Document
        And Для кнопки задать Имя OpenDocumentRead
        And В форме элемента на ленте задать Действие Read
        When Сохранить изменения
        And Перейти во вкладку Дерево в форме
        When В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\Reports
        Then Проверить визуальные изменения для вкладки Reports после добавления вкладки
        When В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\Reports\DocumentCommands
        Then Проверить визуальные изменения для группы DocumentCommands после добавления группы в ленту
        When В дереве формы справочника перейти в узел Форма\Лента\DefaultCategory\Reports\DocumentCommands\OpenDocumentRead
        Then Проверить визуальные изменения для кнопки OpenDocumentRead действия Read после добавления кнопки в группу
        And Проверить метаданные после добавления вкладки в панель вкладок
        When Открыть список изменений
        Then Проверить отображение сделанных изменений в списке изменений после создания вкладки в панель вкладок через Drag and drop

    Examples:
        | moduleName   | entityName     |
        | ChangeModule | FormEditorTest |