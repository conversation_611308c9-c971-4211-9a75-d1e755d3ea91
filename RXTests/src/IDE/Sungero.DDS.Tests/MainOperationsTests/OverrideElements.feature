Feature: OverrideElements
Тесты на перекрытие элементов в DevelopmentStudio

	@AfterMainOperationsTest
    Scenario Outline: Перекрытие модуля
        Given Запустить DevelopmentStudio
        When Перекрыть Модуль <moduleName> в решении <solutionName>
        Then Проверить состав файлов перекрытого модуля
        When Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после перекрытия модуля

    Examples:
        | solutionName | moduleName        |
        | OverrideTest | IntegrationModule |

    @AfterMainOperationsTest
    Scenario Outline: Перекрытие документа
        Given Запустить DevelopmentStudio
        When Перекрыть Документ <documentName> в решении <solutionName>
        Then Проверить состав файлов перекрытого документа
        When Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после перекрытия документа

    Examples:
        | solutionName | documentName                             |
        | OverrideTest | Sungero.IntegrationModule.SimpleDocument |

    @AfterMainOperationsTest
    Scenario Outline: Перекрытие справочника
        Given Запустить DevelopmentStudio
        When Перекрыть Справочник <databookName> в решении <solutionName>
        Then Проверить состав файлов перекрытого справочника
        When Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после перекрытия справочника

    Examples:
        | solutionName | databookName              |
        | OverrideTest | Sungero.Events.ParamsBook |

    @AfterMainOperationsTest
    Scenario Outline: Перекрытие задачи
        Given Запустить DevelopmentStudio
        When Перекрыть Задача <taskName> в решении <solutionName>
        Then Проверить состав файлов перекрытого задачи
        When Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после перекрытия задачи

    Examples:
        | solutionName | taskName                           |
        | OverrideTest | Sungero.MeetingModule.AutoTextTask |

    @AfterMainOperationsTest
    Scenario Outline: Перекрытие задания
        Given Запустить DevelopmentStudio
        When Перекрыть Задание <assignmentName> в решении <solutionName>
        Then Проверить состав файлов перекрытого задания в задаче <taskName>
        When Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после перекрытия задания

    Examples:
        | solutionName | assignmentName                           | taskName     |
        | OverrideTest | Sungero.MeetingModule.AutoTextAssignment | AutoTextTask |

    @AfterMainOperationsTest
    Scenario Outline: Перекрытие уведомления
        Given Запустить DevelopmentStudio
        When Перекрыть Уведомление <noticeName> в решении <solutionName>
        Then Проверить состав файлов перекрытого уведомления в задаче <taskName>
        When Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после перекрытия уведомления

    Examples:
        | solutionName | noticeName                              | taskName            |
        | OverrideTest | Sungero.MeetingModule.MeetingUserNotice | MeetingApprovalTask |

    @AfterMainOperationsTest
    Scenario Outline: Удалить перекрытые элементы
        Given Запустить DevelopmentStudio
        When Удалить перекрытые элементы в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления перекрытий
        And Проверить состав файлов после удаления перекрытых сущностей

    Examples:
        | solutionName |
        | DeleteTest   |

    @AfterMainOperationsTest
    Scenario Outline: Удалить перекрытый модуль
        Given Запустить DevelopmentStudio
        When Удалить перекрытый модуль <moduleName> в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления перекрытого модуля
        And Проверить состав файлов после удаления перекрытого модуля

    Examples:
        | solutionName | moduleName        |
        | DeleteTest   | IntegrationModule |

    @AfterMainOperationsTest
    Scenario Outline: Удалить перекрытый документ
        Given Запустить DevelopmentStudio
        When Удалить перекрытый документ <documentName> в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления перекрытого документа
        And Проверить состав файлов после удаления перекрытого документа

    Examples:
        | solutionName | documentName   |
        | DeleteTest   | SimpleDocument |

    @AfterMainOperationsTest
    Scenario Outline: Удалить перекрытый справочник
        Given Запустить DevelopmentStudio
        When Удалить перекрытый справочник <databookName> в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления перекрытого справочника
        And Проверить состав файлов после удаления перекрытого справочника

    Examples:
        | solutionName | databookName |
        | DeleteTest   | MeetingUser  |

    @AfterMainOperationsTest
    Scenario Outline: Удалить одну перекрытую задачу
        Given Запустить DevelopmentStudio
        When Удалить перекрытый задачу <taskName> в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления одной перекрытой задачи
        And Проверить состав файлов после удаления перекрытой задачи

    Examples:
        | solutionName | taskName       |
        | DeleteTest   | TaskMonitoring |

    @AfterMainOperationsTest
    Scenario Outline: Удалить перекрытую задачу
        Given Запустить DevelopmentStudio
        When Удалить перекрытый задачу <taskName> в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления перекрытой задачи
        And Проверить состав файлов после удаления перекрытой задачи

    Examples:
        | solutionName | taskName     |
        | DeleteTest   | AutoTextTask |

    @AfterMainOperationsTest
    Scenario Outline: Удалить перекрытое задание
        Given Запустить DevelopmentStudio
        When Удалить перекрытый задание <assignmentName> в решении <solutionName>
        And Собрать решение
        Then Ошибок при сборке не должно быть
        When Открыть список изменений
        Then Проверить изменения в списке изменений после удаления перекрытого задания
        And Проверить состав файлов после удаления перекрытого задания

    Examples:
        | solutionName | assignmentName     |
        | DeleteTest   | AutoTextAssignment |