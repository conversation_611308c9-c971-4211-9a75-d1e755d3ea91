using Autotest.DDS.Utils;
using Autotest.Web.Utils;
using Reqnroll;
using Sungero.AutoTestObjectsDSDesktop.Editors.Core;
using Sungero.AutoTestObjectsDSDesktop.SpecflowSteps;

namespace Sungero.DSDesktop.Tests.Hooks
{
  [Binding]
  public class CreateModuleBlocksHooks : BaseHooks
  {
    #region Before
    
    [BeforeScenario("BeforeСозданиеБлокаЗадание")]
    public void BeforeScenarioCreateAssignmentBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var blockName = this.GenerateTestName("CreatedAssignmentBlock");
        var changedParameterName = BlockEditorBase.DisplayName;
        var changedParameterValue = this.GenerateTestName(changedParameterName);

        ScContext.Add(BlockEditorSteps.Context.ParameterValue, changedParameterValue);
        ScContext.Add(BlockEditorSteps.Context.BlockName, blockName);
        ScContext.Add(BlocksTreeSteps.Context.BlockType, DDSConstants.MetadataItemTypes.AssignmentBlock);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }

    [BeforeScenario("BeforeСозданиеБлокаМониторинг")]
    public void BeforeScenarioCreateMonitoringBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var blockName = this.GenerateTestName("CreatedMonitoringBlock");
        var changedParameterName = BlockEditorBase.DisplayName;
        var changedParameterValue = this.GenerateTestName(changedParameterName);

        ScContext.Add(BlockEditorSteps.Context.ParameterValue, changedParameterValue);
        ScContext.Add(BlockEditorSteps.Context.BlockName, blockName);
        ScContext.Add(BlocksTreeSteps.Context.BlockType, DDSConstants.MetadataItemTypes.MonitoringBlock);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }

    #endregion

    #region Конструктор

    public CreateModuleBlocksHooks(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion
  }
}
