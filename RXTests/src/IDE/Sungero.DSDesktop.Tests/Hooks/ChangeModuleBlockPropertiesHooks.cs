using Autotest.DDS.Utils;
using Autotest.Web.Utils;
using Reqnroll;
using Sungero.AutoTestObjectsDSDesktop.Editors.Core;
using Sungero.AutoTestObjectsDSDesktop.SpecflowSteps;
using System.Collections.Generic;

namespace Sungero.DSDesktop.Tests.Hooks
{
  [Binding]
  public class ChangeModuleBlockPropertiesHooks : BaseHooks
  {
    #region Before
    
    [BeforeScenario("BeforeИзменениеТипаСущностиДляСвойстваТипаКоллекцияСсылокБлокаЗадания")]
    public void BeforeScenarioChangeEntityTypeCollectionLinksPropertyInAssignmentBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var entityTypeFullName = $"{DDSConstants.CompanyCode}.{DDSConstants.ModuleName.EeTestModule}.{DDSConstants.EntityName.TestDocument}";
        this.AddBlockPropertyToContext("AssignmentBlock", "PropertyCollectionLinks");
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterName, BlockPropertyEditor.EntityType);
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterValue, entityTypeFullName);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }
    
    [BeforeScenario("BeforeДобавлениеЗначенияПеречисленияВСвойствеБлокаМониторинг")]
    public void BeforeScenarioAddEnumPropertyValueInMonitoringBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var enumValues = new Dictionary<string, string>()
        {
          { GenerateTestName("Enum"), GenerateTestName("DisplayName") }
        };

        this.AddBlockPropertyToContext("MonitoringBlock", "PropertyEnum");
        ScContext.Add(BlockPropertyEditorSteps.Context.EnumValues, enumValues);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }
    
    [BeforeScenario("BeforeИзменениеОбязательностиДляЦелочисленногоСвойстваБлокаУведомление")]
    public void BeforeScenarioChangeIsRequiredIntPropertyInNoticeBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        this.AddBlockPropertyToContext("NoticeBlock", "PropertyInt");
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }

    [BeforeScenario("BeforeИзменениеПараметраОтображатьВНастройкахВЛогическомСвойствеБлокаСкрипт")]
    public void BeforeScenarioChangeIsVisibleBoolPropertyInScriptBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        this.AddBlockPropertyToContext("ScriptBlock", "PropertyBool");
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }
    
    [BeforeScenario("BeforeИзменениеТипаВещественногоСвойстваБлокаЗадача")]
    public void BeforeScenarioChangeTypeDoublePropertyInTaskBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        this.AddBlockPropertyToContext("TaskBlock", "PropertyDouble");
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterName, BlockPropertyEditor.PropertyType);
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterValue, "Целое");
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }
    
    [BeforeScenario("BeforeИзменениеТипаСущностиВВыходномСвойствеБлокаУведомление")]
    public void BeforeScenarioChangeEntityTypeOutPropertyInNoticeBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var entityTypeFullName = $"{DDSConstants.CompanyCode}.{DDSConstants.ModuleName.DSTestModule}.{DDSConstants.EntityName.DSDatabook}";
        this.AddBlockPropertyToContext("NoticeBlock", "OutPropertyLink");
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterName, BlockPropertyEditor.EntityType);
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterValue, entityTypeFullName);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }
    
    [BeforeScenario("BeforeИзменениеИмениВыходногоСвойстваБлокаЗадание")]
    public void BeforeScenarioChangeNameOutPropertyInAssignmentBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var changedPropertyName = GenerateTestName("OutProperty");

        this.AddBlockPropertyToContext("AssignmentBlock", "OutPropertyDateTime");
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterName, BlockPropertyEditor.Name);
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterValue, changedPropertyName);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }

    [BeforeScenario("BeforeИзменениеОтображаемогоИмениВыходногоСвойстваБлокаМониторинг")]
    public void BeforeScenarioChangeDisplayNameOutPropertyInMonitoringBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        var changedPropertyDisplayName = GenerateTestName("OutProperty");

        this.AddBlockPropertyToContext("MonitoringBlock", "OutPropertyStr");
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterName, BlockPropertyEditor.DisplayName);
        ScContext.Add(BlockPropertyEditorSteps.Context.ChangedParameterValue, changedPropertyDisplayName);
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }

    [BeforeScenario("BeforeИзменениеПорядкаЗначенийПеречисленияВСвойствеБлокаМониторинг")]
    public void BeforeScenarioChangeOrderEnumValueInEnumerationPropertyInMonitoringBlock()
    {
      using (new TestLogs("Заполнение ScContext"))
      {
        ScContext.Add(BlockPropertyEditorSteps.Context.EnumName, "Enum1");
        this.AddBlockPropertyToContext("MonitoringBlock", "PropertyEnum");
        this.AddSolutionTreeNodeIdsToContext(
          DDSConstants.TestEntityIds.SDSTestsSolutionId,
          DDSConstants.TestEntityIds.EeTestModuleId);
      }
    }

    #endregion

    #region Конструктор

    public ChangeModuleBlockPropertiesHooks(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion
  }
}
