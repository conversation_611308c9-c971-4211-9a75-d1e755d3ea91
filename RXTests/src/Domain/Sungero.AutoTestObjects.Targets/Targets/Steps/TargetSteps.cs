using Autotest.Web.Utils;
using FluentAssertions;
using Reqnroll;
using Sungero.AutoTestObjects.Base;

namespace Sungero.AutoTestObjects.Targets.Targets.Steps
{
  [Binding]
  public class TargetSteps : AutoTestObjects.Steps
  {
    #region Then

    [Then("Target. Дочерняя цель должна быть связана с основной")]
    public void CheckLinkBetweenChildAndMainTarget()
    {
      using (new TestLogs("Дочерняя цель должна быть связана с основной"))
      {
        var childTargetName = ScContext.Get<string>(Context.ChildTargetName);
        var childTarget = Databookentry.GetByName<Target>(childTargetName);
        var leadingTargetName = ScContext.Get<string>(Context.LeadingTargetName);
        var leadingTarget = Databookentry.GetByName<Target>(leadingTargetName);

        childTarget.LeadingTarget.Id.Should().Be(leadingTarget.Id, "Дочерняя цель должна быть связана с основной");
      }
    }

    #endregion

    #region Вложенный класс

    public class Context
    {
      public const string ChildTargetName = "ChildTargetName";
      public const string LeadingTargetName = "LeadingTargetName";
    }

    #endregion

    #region Конструктор

    public TargetSteps(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion
  }
}