using System.Collections.Generic;
using Autotest.Web.Utils;
using Sungero.AutotestObjects.Web.Controls.Properties;
using Sungero.AutoTestObjectsRX.Meetings;
using Sungero.AutoTestObjectsRX.Web.Docflow;

namespace Sungero.AutoTestObjectsRX.Web.Meetings
{
  public class AgendaCard : InternalDocumentBaseCard
  {
    #region Поля и свойства

    protected override string caption => Agenda.DisplayName;



    public override IDictionary<string, BaseControl> SimpleProperties
    {
      get
      {
        if (this.simpleProperties != null) return this.simpleProperties;
        var result = base.SimpleProperties;
        result[Agenda.PropertyNames.Meeting] = new NavigationControl(this.Card, Agenda.LocalizedProperties.Meeting);
        return result;
      }
    }

    #endregion

    #region Методы

    public void OpenMeetingList()
    {
      this.OpenEntityCollection(Minutes.LocalizedProperties.Meeting);
      var modalGrid = new MeetingModalGrid(Meeting.CollectionDisplayName);
      this.WaitIsLoadData();
      if (!modalGrid.Exists())
        throw new WebGuiException("Модальный список с совещаниями не был открыт.");
      TestLogs.Screen();
    }

    #endregion

    #region Конструкторы

    public AgendaCard(string caption) : base(caption)
    {
    }

    public AgendaCard() : base()
    {
    }

    #endregion
  }
}