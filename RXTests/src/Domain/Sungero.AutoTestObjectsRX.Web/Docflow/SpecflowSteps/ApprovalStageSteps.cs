using System.Collections.Generic;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using FluentAssertions;
using Sungero.AutotestObjects.Web;
using Sungero.AutotestObjects.Web.Controls.Properties;
using Sungero.AutotestObjects.Web.SpecflowSteps;
using Sungero.AutoTestObjectsRX.Docflow;
using Reqnroll;

namespace Sungero.AutoTestObjectsRX.Web.Docflow.SpecflowSteps
{
  [Binding]
  public class ApprovalStageSteps : WebSteps
  {

	  #region Методы

    [Then(@"ApprovalStageCard. Проверить заполнение полей на доработку после создания этапа с типом Регистрация")]
    public void CheckReworkParamsAvailabilityForRegistration()
    {
      using (new TestLogs("Проверить заполнение полей на доработку после создания этапа с типом Регистрация", true))
      {
        var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);
        var isAllowReworkReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.AllowSendToRework);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowSendToRework} доступна в карточке этапа", isAllowReworkReadOnly);

        var isAllowReworkNotChecked = !card.GetCheckBoxPropertyValue(ApprovalStage.PropertyNames.AllowSendToRework);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowSendToRework} снята", isAllowReworkNotChecked);

        var isReworkTypeReadOnly = card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkType} не доступно для редактирования в карточке этапа", isReworkTypeReadOnly);

        var isReworkPerformerTypeReadOnly = card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkPerformerType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} не доступно для редактирования в карточке этапа",
          isReworkPerformerTypeReadOnly);

        var isAllowChangeReworkPerfReadOnly = card.PropertyIsReadOnly(ApprovalStage.PropertyNames.AllowChangeReworkPerformer);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowChangeReworkPerformer} не доступна в карточке этапа",
          isAllowChangeReworkPerfReadOnly);

        (isAllowReworkReadOnly && isAllowReworkNotChecked && isReworkTypeReadOnly && isReworkPerformerTypeReadOnly &&
                      isAllowChangeReworkPerfReadOnly).Should().BeTrue();
      }
    }

    [Then(@"ApprovalStageCard. Проверить заполнение полей на доработку после создания этапа с типом Согласование")]
    public void CheckReworkParamsAvailabilityForApproval()
    {
      using (new TestLogs("Проверить заполнение полей на доработку после создания этапа с типом Согласование", true))
      {
        var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);
        var isReworkTypeReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkType} доступно для редактирования в карточке этапа", isReworkTypeReadOnly);

        var isReworkTypeCorrect = card.SimpleProperties[ApprovalStage.PropertyNames.ReworkType].GetValue()
	        .Equals(ApprovalStage.ReworkTypes.AfterAll);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkType} заполнено значением {ApprovalStage.ReworkTypes.AfterAll}", isReworkTypeCorrect);

        var isReworkPerformerTypeReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkPerformerType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} доступно для редактирования в карточке этапа",
          isReworkPerformerTypeReadOnly);

        var isReworkPerfTypeCorrect = card.SimpleProperties[ApprovalStage.PropertyNames.ReworkPerformerType].GetValue()
	        .Equals(ApprovalStage.ReworkPerformerTypes.FromRule);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} заполнено значением {ApprovalStage.ReworkPerformerTypes.FromRule}", isReworkPerfTypeCorrect);

        var isAllowChangeReworkPerfReadOnly = card.PropertyIsReadOnly(ApprovalStage.PropertyNames.AllowChangeReworkPerformer);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowChangeReworkPerformer} не доступна в карточке этапа",
          isAllowChangeReworkPerfReadOnly);

        (isReworkTypeReadOnly && isReworkTypeCorrect && isReworkPerformerTypeReadOnly && isReworkPerfTypeCorrect &&
                      isAllowChangeReworkPerfReadOnly).Should().BeTrue();
      }
    }

    [Then(@"ApprovalStageCard. Проверить заполнение полей после создания этапа с типом Подписание")]
    public void CheckSignStage()
    {
      var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);

      var isNeedStrongSignCorrect = card.GetCheckBoxPropertyValue(ApprovalStage.PropertyNames.NeedStrongSign);
	    Logs.Check("Галочка {0} должна быть выбрана", isNeedStrongSignCorrect, ApprovalStage.LocalizedProperties.NeedStrongSign);

	    var isAssigneeTypeCorrect = card.GetPropertyValue(ApprovalStage.PropertyNames.AssigneeType)
		    .Equals(ApprovalStage.AssigneeTypes.Role);
	    Logs.Check("Проверить автозаполнение свойства {0}", isAssigneeTypeCorrect, ApprovalStage.LocalizedProperties.AssigneeType);

	    var isApprovalRoleCorrect = card.GetPropertyValue(ApprovalStage.PropertyNames.ApprovalRole).Equals(ApprovalRoleBase.Entities.Signatory);
	    Logs.Check("Проверить автозаполнение свойства {0}", isApprovalRoleCorrect, ApprovalStage.LocalizedProperties.ApprovalRole);

	    (isNeedStrongSignCorrect && isAssigneeTypeCorrect && isApprovalRoleCorrect).Should().BeTrue("Все поля должны быть заполнены.");
    }

    [Then(@"ApprovalStageCard. Проверить заполнение полей этапа с типом Согласование с руководителем")]
    public void CheckManagerStage()
    {
	    var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);

	    var isNeedStrongSignCorrect = card.GetCheckBoxPropertyValue(ApprovalStage.PropertyNames.NeedStrongSign);
	    Logs.Check("Галочка {0} не должна быть выбрана", isNeedStrongSignCorrect, ApprovalStage.LocalizedProperties.NeedStrongSign);

	    var isAssigneeTypeCorrect = card.GetPropertyValue(ApprovalStage.PropertyNames.AssigneeType)
		    .Equals(ApprovalStage.AssigneeTypes.Role);
	    Logs.Check("Проверить автозаполнение свойства {0}", isAssigneeTypeCorrect, ApprovalStage.LocalizedProperties.AssigneeType);

      var isApprovalRoleCorrect = card.GetPropertyValue(ApprovalStage.PropertyNames.ApprovalRole).Equals(ApprovalRoleBase.Entities.ManagerOfInitiator);
	    Logs.Check("Проверить автозаполнение свойства {0}", isApprovalRoleCorrect, ApprovalStage.LocalizedProperties.ApprovalRole);

	    (!isNeedStrongSignCorrect && isAssigneeTypeCorrect && isApprovalRoleCorrect).Should().BeTrue();
    }

    [Given(@"^ApprovalStageCard. Указать тип этапа (Регистрация|Согласование|Подписание|Согласование с руководителем)$")]
    public void SetStageType(string stageType)
    {
	    var stageProperties = ScContext.Get<Dictionary<string, string>>(CardPageSteps.Context.PropertyValues);
	    var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);
	    var stageTypeProperty = ApprovalStage.PropertyNames.StageType;

      using (new TestLogs($"Указать тип этапа {stageType}", true))
	    {
		    switch (stageType)
		    {
			    case "Регистрация":
				    stageProperties.Add(stageTypeProperty, ApprovalStage.AssignmentStageTypes.Register);
				    card.SimpleProperties[stageTypeProperty].SetValue(stageProperties[stageTypeProperty]);
				    break;
			    case "Согласование":
				    stageProperties.Add(stageTypeProperty, ApprovalStage.AssignmentStageTypes.Approvers);
            stageProperties.Add(ApprovalStage.PropertyNames.ApprovalRoles, ApprovalRoleBase.Entities.ContractResp);
				    var prop = card.SimpleProperties[stageTypeProperty] as NavigationControl;
				    prop.SetValueWithCompleteMatch(stageProperties[stageTypeProperty]);
				    card.SimpleProperties[ApprovalStage.PropertyNames.ApprovalRoles].SetValue(stageProperties[ApprovalStage.PropertyNames.ApprovalRoles]);
            break;
			    case "Подписание":
				    stageProperties.Add(stageTypeProperty, ApprovalStage.AssignmentStageTypes.Sign);
				    card.SimpleProperties[stageTypeProperty].SetValue(stageProperties[stageTypeProperty]);
				    break;
			    case "Согласование с руководителем":
				    stageProperties.Add(stageTypeProperty, ApprovalStage.AssignmentStageTypes.Manager);
				    card.SimpleProperties[stageTypeProperty].SetValue(stageProperties[stageTypeProperty]);
				    break;
        }
	    }
    }

    [When(@"ApprovalStageCard. Сменить порядок доработки")]
    public void ChangeReworkType()
    {
      using (new TestLogs("Сменить порядок доработки", true))
      {
        var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);
        card.SimpleProperties[ApprovalStage.PropertyNames.ReworkType].SetValue(ApprovalStage.ReworkTypes.AfterEach);
      }
    }

    [When(@"ApprovalStageCard. Разрешить отправку на доработку")]
    public void SetAllowReworkForStage()
    {
      using (new TestLogs("Разрешить отправку на доработку", true))
      {
        var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);
        var control = (BooleanControl)card.GetProperty(ApprovalStage.PropertyNames.AllowSendToRework);
        control.Check();
      }
    }

    [Then(@"ApprovalStageCard. Проверить заполнение полей на доработку после смены порядка доработки")]
    public void CheckReworkParamsAfterReworkTypeChange()
    {
      using (new TestLogs("Проверить заполнение полей на доработку после смены порядка доработки", true))
      {
        var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);

        var isReworkPerformerTypeReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkPerformerType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} доступно для редактирования в карточке этапа",
          isReworkPerformerTypeReadOnly);

        var fromRule = ApprovalStage.ReworkPerformerTypes.FromRule;
        var isReworkPerfTypeCorrect = card.SimpleProperties[ApprovalStage.PropertyNames.ReworkPerformerType].GetValue().Equals(fromRule);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} заполнено значением {fromRule}", isReworkPerfTypeCorrect);

        var isAllowChangeReworkPerfReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.AllowChangeReworkPerformer);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowChangeReworkPerformer} не доступна в карточке этапа",
          isAllowChangeReworkPerfReadOnly);

        var isAllowChangeReworkPerfNotChecked = !card.GetCheckBoxPropertyValue(ApprovalStage.PropertyNames.AllowChangeReworkPerformer);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowChangeReworkPerformer} снята", isAllowChangeReworkPerfNotChecked);

        (isReworkPerformerTypeReadOnly && isReworkPerfTypeCorrect &&
         isAllowChangeReworkPerfReadOnly && isAllowChangeReworkPerfNotChecked).Should().BeTrue();
      }
    }

    [Then(@"ApprovalStageCard. Проверить заполнение полей на доработку после разрешения отправки на доработку")]
    public void CheckReworkParamsAfterAllowingRework()
    {
      using (new TestLogs("Проверить заполнение полей на доработку после разрешения отправки на доработку", true))
      {
        var card = ScContext.Get<ApprovalStageCard>(CardPageSteps.Context.Card);
        var isReworkTypeReadOnly = card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkType} не доступно для редактирования в карточке этапа", isReworkTypeReadOnly);

        var afterComplete = ApprovalStage.ReworkTypes.AfterComplete;
        var isReworkTypeCorrect = card.SimpleProperties[ApprovalStage.PropertyNames.ReworkType].GetValue().Equals(afterComplete);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkType} заполнено значением {afterComplete}", isReworkTypeCorrect);

        var isReworkPerformerTypeReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.ReworkPerformerType);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} доступно для редактирования в карточке этапа",
          isReworkPerformerTypeReadOnly);

        var fromRule = ApprovalStage.ReworkPerformerTypes.FromRule;
        var isReworkPerfTypeCorrect = card.SimpleProperties[ApprovalStage.PropertyNames.ReworkPerformerType].GetValue().Equals(fromRule);
        Logs.Check($"Поле {ApprovalStage.LocalizedProperties.ReworkPerformerType} заполнено значением {fromRule}", isReworkPerfTypeCorrect);

        var isAllowChangeReworkPerfReadOnly = !card.PropertyIsReadOnly(ApprovalStage.PropertyNames.AllowChangeReworkPerformer);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowChangeReworkPerformer} доступна в карточке этапа",
          isAllowChangeReworkPerfReadOnly);

        var isAllowChangeReworkPerfNotChecked = !card.GetCheckBoxPropertyValue(ApprovalStage.PropertyNames.AllowChangeReworkPerformer);
        Logs.Check($"Галочка {ApprovalStage.LocalizedProperties.AllowChangeReworkPerformer} снята", isAllowChangeReworkPerfNotChecked);

        (isReworkTypeReadOnly && isReworkTypeCorrect && isReworkPerformerTypeReadOnly && isReworkPerfTypeCorrect &&
                      isAllowChangeReworkPerfReadOnly
                      && isAllowChangeReworkPerfNotChecked).Should().BeTrue();
      }
    }

    [Then(@"ApprovalStage. Проверить заполнение полей после сохранения этапа с типом Регистрация")]
    public void AssignmentStageWithRegisterTypeAndReworkShouldBeCreated()
    {
      using (new TestLogs("Проверить заполнение полей после сохранения этапа с типом Регистрация"))
      {
        var appCardParams = ScContext.Get<Dictionary<string, string>>(CardPageSteps.Context.PropertyValues);
        var stage = ApprovalStage.Get(appCardParams[ApprovalStage.PropertyNames.Name]);

        var checks = Logs.CheckWithReturnValue($"Имя должно быть {appCardParams[ApprovalStage.PropertyNames.Name]}",
          stage.Name.Equals(appCardParams[ApprovalStage.PropertyNames.Name]));
        checks = checks && Logs.CheckWithReturnValue($"Тип этапа согласования должен быть {ApprovalStage.StageTypeEnum.Register}",
          stage.StageType.Equals(ApprovalStage.StageTypeEnum.Register));
        checks = checks && Logs.CheckWithReturnValue($"Срок должен быть равен {appCardParams[ApprovalStage.PropertyNames.DeadlineInDays]}",
          stage.DeadlineInDays.Equals(int.Parse(appCardParams[ApprovalStage.PropertyNames.DeadlineInDays])));
        checks = checks && Logs.CheckWithReturnValue($"Галочка \"Разрешить отправку на доработку\" должна быть {stage.AllowSendToRework.Value}",
          stage.AllowSendToRework != null && stage.AllowSendToRework.Value);
        checks = checks && Logs.CheckWithReturnValue($"Тип доработки должен быть {ApprovalStage.ReworkTypeEnum.AfterComplete}",
          stage.ReworkType.Equals(ApprovalStage.ReworkTypeEnum.AfterComplete));
        checks = checks && Logs.CheckWithReturnValue($"Исполнитель доработки должен быть {ApprovalStage.ReworkPerformerTypeEnum.FromRule}",
          stage.ReworkPerformerType.Equals(ApprovalStage.ReworkPerformerTypeEnum.FromRule));
        checks = checks && Logs.CheckWithReturnValue($"Галочка \"Разрешить изменение исполнителя доработки\" должна быть {stage.AllowChangeReworkPerformer.Value}",
          !(stage.AllowChangeReworkPerformer != null && stage.AllowChangeReworkPerformer.Value));

        checks.Should().BeTrue();
      }
    }

    [Then(@"ApprovalStage. Проверить заполнение полей после сохранения этапа с типом Согласование")]
    public void AssignmentStageWithApprovalTypeAndReworkShouldBeCreated()
    {
      using (new TestLogs("Проверить заполнение полей после сохранения этапа с типом Согласование"))
      {
        var appCardParams = ScContext.Get<Dictionary<string, string>>(CardPageSteps.Context.PropertyValues);
        var stage = ApprovalStage.Get(appCardParams[ApprovalStage.PropertyNames.Name]);

        var checks = Logs.CheckWithReturnValue($"Имя должно быть {appCardParams[ApprovalStage.PropertyNames.Name]}",
          stage.Name.Equals(appCardParams[ApprovalStage.PropertyNames.Name]));
        checks = checks && Logs.CheckWithReturnValue($"Тип этапа согласования должен быть {ApprovalStage.StageTypeEnum.Approvers}",
          stage.StageType.Equals(ApprovalStage.StageTypeEnum.Approvers));
        checks = checks && Logs.CheckWithReturnValue($"Срок должен быть равен {appCardParams[ApprovalStage.PropertyNames.DeadlineInDays]}",
          stage.DeadlineInDays.Equals(int.Parse(appCardParams[ApprovalStage.PropertyNames.DeadlineInDays])));
        checks = checks && Logs.CheckWithReturnValue($"Тип доработки должен быть {ApprovalStage.ReworkTypeEnum.AfterEach}",
          stage.ReworkType.Equals(ApprovalStage.ReworkTypeEnum.AfterEach));
        checks = checks && Logs.CheckWithReturnValue($"Исполнитель доработки должен быть {ApprovalStage.ReworkPerformerTypeEnum.FromRule}",
          stage.ReworkPerformerType.Equals(ApprovalStage.ReworkPerformerTypeEnum.FromRule));
        checks = checks && Logs.CheckWithReturnValue($"Галочка \"Разрешить изменение исполнителя доработки\" должна быть {stage.AllowChangeReworkPerformer.Value}",
          !(stage.AllowChangeReworkPerformer != null && stage.AllowChangeReworkPerformer.Value));

        checks.Should().BeTrue();
      }
    }

    [Then(@"^ApprovalStage. Проверить заполнение полей после сохранения этапа с типом (Подписание|Согласование с руководителем)$")]
    public void AssignmentStageWithManagerTypeShouldBeCreated(string assignmentStageType)
    {
      using (new TestLogs("Проверить заполнение полей после сохранения этапа с типом Подписание"))
      {
        var appCardParams = ScContext.Get<Dictionary<string, string>>(CardPageSteps.Context.PropertyValues);
        var stage = ApprovalStage.Get(appCardParams[ApprovalStage.PropertyNames.Name]);

        var approvalStageTypes = assignmentStageType == "Подписание"
          ? ApprovalStage.StageTypeEnum.Sign
          : ApprovalStage.StageTypeEnum.Manager;

        var checks = Logs.CheckWithReturnValue($"Имя должно быть {appCardParams[ApprovalStage.PropertyNames.Name]}",
          stage.Name.Equals(appCardParams[ApprovalStage.PropertyNames.Name]));
        checks = checks && Logs.CheckWithReturnValue($"Тип этапа согласования должен быть {approvalStageTypes}",
          stage.StageType.Equals(approvalStageTypes));
        checks = checks && Logs.CheckWithReturnValue($"Срок должен быть равен {appCardParams[ApprovalStage.PropertyNames.DeadlineInDays]}",
          stage.DeadlineInDays.Equals(int.Parse(appCardParams[ApprovalStage.PropertyNames.DeadlineInDays])));

        checks.Should().BeTrue($"Этап согласования с типом {assignmentStageType} должен быть создан с нужными параметрами.");
      }
    }

    #endregion

    #region Конструкторы

    public ApprovalStageSteps(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion

  }
}