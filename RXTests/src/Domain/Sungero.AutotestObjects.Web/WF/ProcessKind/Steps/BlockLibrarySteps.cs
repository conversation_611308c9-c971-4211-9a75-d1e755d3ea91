using System;
using Autotest.Web.Utils;
using Sungero.AutotestObjects.Web.SpecflowSteps;
using Reqnroll;

namespace Sungero.AutotestObjects.Web.WF.ProcessKind.Steps
{
  [Binding]
  public class BlockLibrarySteps : WebSteps
  {
    public static class Context
    {
      public static string BlockType = "BlockType";
      
      public static string BlockGuid = "BlockGuid";
    }
    
    [StepDefinition(@"BlockLibrary. Добавить базовый блок (.*) на схему")]
    public void WhenAddBaseBlockToScheme(string type)
    { 
      using (new TestLogs($"Добавить базовый блок {type} на схему.", true))
      {
        this.AddBlockToScheme();
      }
    }

    [StepDefinition(@"BlockLibrary. Добавить прикладной блок (модуля|задачи) (.*) на схему")]
    public void WhenAddAppliedBlockToScheme(string entityWithAppliedBlocks, string type)
    {
      using (new TestLogs($"Добавить блок {entityWithAppliedBlocks} {type} на схему.", true))
      {
        this.AddBlockToScheme();
      }
    }

    private void AddBlockToScheme()
    {
      var blockGuid = ScContext.Get<string>(Context.BlockGuid);
      var blockType = ScContext.Get<string>(Context.BlockType);
      var card = ScContext.Get<ProcessKindCard>(CardPageSteps.Context.Card);
      card.SchemaEditor.DragNDropBlockToScheme(blockType, blockGuid, 0, 0);
      card.WaitEndLongOperation(TimeSpan.FromMilliseconds(2000));
    }

    public BlockLibrarySteps(ScenarioContext scContext) : base(scContext)
    {
    }
  }
}