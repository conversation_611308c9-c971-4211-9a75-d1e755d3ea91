using System;
using Autotest.Web.Utils;
using Sungero.AutoTestObjects.Base;
using Sungero.AutotestObjects.Web.Core;
using Reqnroll;
using System.Collections.Generic;
using Autotest.Web.Utils.Selenium;
using OpenQA.Selenium;
using Sungero.AutoTestObjects.SpecflowSteps;
using FluentAssertions;
using Sungero.AutotestObjects.Web.Dialogs;
using Autotest.Base.Utils;
using Sungero.AutoTestObjects.Content;
using Sungero.AutotestObjects.Web.Controls;
using Autotest.Web.Utils.HostsUtilities;

namespace Sungero.AutotestObjects.Web.SpecflowSteps
{ [Binding]
  public sealed class GridPageSteps : WebSteps
  {
    #region Given

    [Given("BaseGrid. Выбрать первую запись по имени сущности")]
    public void SelectEntityByName()
    {
      var entityNames = this.ScContext.Get<List<string>>(EntitiesSteps.Context.EntityNames);
      var grid = this.ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs("Выбрать первую запись по имени сущности", true))
      {
        var docName = new List<string> { entityNames[0] };
        grid.SelectEntitiesByNames(docName);
      }
    }

    [Given("BaseGrid. Выделить первую запись")]
    public void HoverEntityByName()
    {
      var entityNames = this.ScContext.Get<List<string>>(EntitiesSteps.Context.EntityNames);
      var grid = this.ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs("Выделить первую запись по имени сущности", true))
        grid.HoverEntity(entityNames[0]);
    }

    #endregion
    
    #region When
    
    [When("^BaseGrid. Удалить выбранную сущность (с подтверждением|без подтверждения)$")]
    public void DeleteEntityFromList(string type)
    {      
      var grid = ScContext.Get<GridPage>(Context.Grid);     
      using (new TestLogs("Удалить выбранную сущность", true))
      {
        if (type == "с подтверждением")
          grid.DeleteRecord("с подтверждением");
        else
          grid.DeleteRecord("без подтверждения");
      }
    }
    
    [When("BaseGrid. Открыть (.*) кликом по имени")]
    public void ClickHyperlink(string entity)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs($"Открыть {entity} кликом по имени.", true))
      {
        grid.ActiveRow.HyperLink.Click();
      }
    }

    #endregion

    #region Then
    
    [Then("BaseGrid. После удаления список обновился")]
    public void CheckListUpdate()
    {
      using (new TestLogs("После удаления список обновился", true))
      {
        var entityNames = this.ScContext.Get<List<string>>(EntitiesSteps.Context.EntityNames);
        var noErrors = true;
        var grid = this.ScContext.Get<GridPage>(Context.Grid);
        foreach (var doc in entityNames)
        {
          grid.SetQuickFilter(doc);
          if (grid.HasEntity(doc, "Имя")) noErrors = false;
        }
        noErrors.Should().BeTrue();
      }
    }

    [Then("BaseGrid. Удаленные записи пропали из списка")]
    public void CheckDeletedEntities()
    {
      using (new TestLogs("Удаленные записи пропали из списка", true))
      {
        var entityNames = this.ScContext.Get<List<string>>(EntitiesSteps.Context.EntityNames);
        var errors = false;
        var grid = this.ScContext.Get<GridPage>(Context.Grid);
        for (var i = 0; i < entityNames.Count - 1; i++)
        {
          grid.SetQuickFilter(entityNames[i]);

          if (grid.HasEntity(entityNames[i], "Имя"))
            errors = true;
        }
        errors.Should().BeFalse();
      }
    }

    [Then("BaseGrid. Неудаленная запись не пропала из списка")]
    public void CheckSelectedEntity()
    {
      using (new TestLogs("Неудаленная запись не пропала из списка", true))
      {
        var entityNames = this.ScContext.Get<List<string>>(EntitiesSteps.Context.EntityNames);
        var grid = this.ScContext.Get<GridPage>(Context.Grid);
        grid.SetQuickFilter(entityNames[entityNames.Count - 1]);
        var check = grid.RowsCount == 1 || grid.ActiveRow.Selected;
        check.Should().BeTrue();
      }
    }
    
    [Then("BaseGrid. Количество строк должно быть равно количеству полученных сущностей")]
    public void CheckRowsEntity()
    {
      var countEntities = ScContext.Get<int>(EntitiesSteps.Context.Criteries);

      using (new TestLogs($"Количество строк должно быть равно {countEntities}.", true))
      {
        var grid = this.ScContext.Get<GridPage>(Context.Grid);
        grid.RowsCount.Should().Be(countEntities,$"Количество строк должно быть равно {countEntities}.");
      }
    }
    
    [Then("^BaseGrid. В (списке|папке|результатах поиска) должны быть (записи|документы)$")]
    public void CheckRowsCountGreaterThanNumber(string area, string entities)
    {
      using (new TestLogs($"В {area} должны быть {entities}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.RowsCount.Should().BeGreaterThan(0, $"В {area} есть {entities}");
      }
    }
    
    [Then("BaseGrid. Количество строк должно быть равно (.*)")]
    public void CheckRowsCountEqualsNumber(int number)
    {
      using (new TestLogs($"Количество строк должно быть равно {number}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.RowsCount.Should().Be(number, $"Количество строк должно быть равно {number}");
      }
    }

    [Then("BaseGrid. Показался хинт о неудаленной записи")]
    public void CheckHint()
    {
      using (new TestLogs("Показался хинт о неудаленной записи", true))
      {
        var element = this.driver.S(By.Id("context-notification-message_0"), TimeSpan.FromSeconds(10));
        element.Should().NotBeNull();
      }
    }
    
    [Then("BaseGrid. Действия с именем (.*) не должно существовать")]
    public void ActionWithNameNotShouldBe(string actionName)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs($"Действия с именем {actionName} в списке не должно быть", true))
      {
        var actionNotExists = grid.ContextRibbon.ActionByNameNotExist(actionName);
        actionNotExists.Should().BeTrue();
      }
    }

    [Then("BaseGrid. Действие (.*) не должно существовать")]
    public void ActionNotShouldBe(string act)
    {
      var action = ScContext.Get<string>(Context.Action);
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs($"Действие {action} в списке не должно быть доступно", true))
      {
        var actionNotExists = grid.ContextRibbon.ActionNotExist(action);
        actionNotExists.Should().BeTrue();
      }
    }
    
    [Then("BaseGrid. Порядок видимых колонок должен быть по умолчанию")]
    public void CheckOrderColumns()
    {
      using (new TestLogs("Порядок видимых колонок должен быть по умолчанию", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.ExistsAllDefaultColumns().Should().BeTrue("Порядок видимых колонок должен быть по умолчанию");
      }
    }

    [Then("BaseGrid. Колонка (.*) должна быть отсортирована по (возрастанию|убыванию)")]
    public void CheckSortingColumn(string columnName, string sortingType)
    {
      using (new TestLogs($"Колонка {columnName} должна быть отсортирована по {sortingType}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var column = ScContext.Get<string>(EntitiesSteps.Context.Column);
        var sortedType = sortingType == "возрастанию" ? SortType.Ascending : SortType.Descending;
        grid.GetSorting(column).Should().Be(sortedType, $"Колонка '{columnName}' должна быть отсортирована по {sortingType}");
      }
    }
    
    [Then("BaseGrid. В панели настройки внешнего вида списка должен быть флаг (.*)")]
    public void CheckDefaultFilterFlagExist(string flagName)
    {
      using (new TestLogs($"В меню фильтрации должен быть флаг {flagName}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var flag = ScContext.Get<string>(Context.Flag);

        grid.CheckDefaultFilterFlagExist(flag).Should().BeTrue($"В меню фильтрации должен быть флаг {flagName}");
      }
    }

    [Then("BaseGrid. Список (.*) должен быть открыт")]
    public void CheckEntityTypeGrid(string entityType)
    {
      using (new TestLogs($"Список {entityType} должен быть открыт", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var title = ScContext.Get<string>(Context.Title);
        grid.CurrentContext.Should().Be(title);
      }
    }
    
    #endregion
    
    #region Steps

    [StepDefinition("BaseGrid. Актуализирвать количество записей")]
    public void ActualizeRowCoun()
    {
      var grid = this.ScContext.Get<GridPage>(Context.Grid);
      var count = grid.RowsCount;
      if (count != 1000) return;
      using (new TestLogs("Актуализирвать количество записей.", true))
        grid.ActualizeRowCount();
    }
    
     [StepDefinition("BaseGrid. Скопировать ссылку на (.*)")]
    public void CopyEntityLink(string p0)
    {
      using (new TestLogs($"Скопировать ссылку на {p0}", true))
      {
        var value = ScContext.Get<string>(EntitiesSteps.Context.Value);
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var column = ScContext.Get<string>(EntitiesSteps.Context.Column);
        grid.SetQuickFilter(value);
        grid.CopyLink(value, column);
      }
    }

    [StepDefinition("BaseGrid. Вставить ссылку на (.*) в (.*)")]
    public void PasteEntityLink(string p0, string context)
    {
      using (new TestLogs($"Вставить ссылку на {p0} в {context}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var value = ScContext.Get<string>(EntitiesSteps.Context.Value);
        grid.PasteLink(value);
      }
    }

    [StepDefinition("BaseGrid. Filter list (.*) by (.*) name")]
    [StepDefinition("BaseGrid. Отфильтровать список (.*) по имени (.*)")]
    [StepDefinition("BaseGrid. Отфильтровать папку (.*) по имени (.*)")]
    public void SetQuickFilter(string p0, string p1)
    {
      var filter = ScContext.Get<string>(EntitiesSteps.Context.FastFilter);
      using (new TestLogs($"Отфильтровать список {p0} по имени {filter}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);

        grid.SetQuickFilter(filter);
      }
    }

    [StepDefinition("BaseGrid. Open card selected (.*)")]
    [StepDefinition("BaseGrid. Открыть карточку выбранной (.*)")]
    [StepDefinition("BaseGrid. Открыть карточку выбранного (.*)")]
    public void OpenSelectedEntity(string p)
    {
      using (new TestLogs($"Открыть карточку выбранной {p}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var card = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
        grid.OpenSelectedEntity(card);
      }
    }

    [StepDefinition("BaseGrid. Выбрать запись по значению в колонке по умолчанию")]
    public void SelectByValueByDefaultColumn()
    {
      using (new TestLogs("Выбрать запись по значению в колонке по умолчанию", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var value = ScContext.Get<string>(EntitiesSteps.Context.Value);
        var column = grid.DefaultPropertyName;
        grid.SelectEntity(value, column);
      }
    }
    
    [StepDefinition("BaseGrid. Find (.*) by value in column (.*). Open card")]
    [StepDefinition("BaseGrid. Открыть карточку (.*) со значением в колонке (.*)")]
    public void OpenCard(string p0, string p1)
    {
	    var grid = ScContext.Get<GridPage>(Context.Grid);
	    var card = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
	    var value = ScContext.Get<string>(EntitiesSteps.Context.Value);
	    var column = ScContext.Get<string>(EntitiesSteps.Context.Column);

	    if (ScContext.ScenarioInfo.Arguments["displayMode"] != null)
	    {
		    var displayMode = (DisplayMode)Enum.Parse(typeof(DisplayMode), ScContext.ScenarioInfo.Arguments["displayMode"].ToString());
		    card.SetDisplayMode(displayMode);
	    }

      using (new TestLogs($"Открыть карточку {p0} со значением {value} в колонке {column}", true))
      {
        grid.SetQuickFilter(value);
        grid.OpenCard(value, column, card);
      }
    }
    
    [StepDefinition("BaseGrid. Создать (.*) из списка")]
    public void CreateEntityFromList(string act)
    {
      var card = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var action = ScContext.Get<string>(Context.Action);
      using (new TestLogs($"Создать {action} из списка", true))
      {
        grid.Create(action, card);
      }
    }
    
    [StepDefinition("BaseGrid. Открыть документ через совместное редактирование. С конвертацией: (.*)")]
    public void OpenDocumentOnline(bool isConvert)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var actionType = ScContext.Get<ActionType>(Context.ActionType);
      using (new TestLogs("Открыть документ через совместное редактирование.", true))
      {
        var editor = grid.EditSelectedDocument(actionType, isConvert);
        ScContext[OnlineEditorSteps.Context.Editor] = editor;
      }
    }

    [StepDefinition("BaseGrid. Выполнить действие (.*) в контекстном меню")]
    public void RunCommandFromContextMenu(string act)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var action = ScContext.Get<string[]>(Context.Action);
      using (new TestLogs($"Выполнить действие {act} в контекстном меню", true))
      {
        if (action.Length > 1)
	        grid.RunContextMenuCommand(action[0], action[1]);
        else
					grid.RunContextMenuCommand(action[0]);
      }
    }

    [StepDefinition("BaseGrid. Выполнить выпадающее действие (.*) на ленте")]
    public void SelectActionOnRibbon(string action)
    {
      using (new TestLogs($"Выполнить выпадающее действие {action} на ленте", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var actionsPath = ScContext.Get<string[]>(Context.ActionsPath);
        grid.MainRibbon.ClickMenuActionByName(actionsPath);
        grid.WaitEndLongOperation();
      }
    }

    [StepDefinition("BaseGrid. Выполнить действие (.*) на риббоне списка с подтверждением")]
    public void RunCommandListRibbonWithConfirm(string menuNames)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);    
      using (new TestLogs("Выполнить действие", true))
      {
        grid.ClickAndConfirm(new []{ menuNames });
      }
    }

    [StepDefinition("BaseGrid. Выполнить действие (.*) на риббоне списка")]
    public void RunCommandListRibbon(string menuNames)
    {
	    var grid = ScContext.Get<GridPage>(Context.Grid);
	    var action = ScContext.Get<string>(Context.Action);

	    using (new TestLogs("Выполнить действие", true))
	    {
		    grid.ClickFastByName(action);
	    }
    }

    [StepDefinition("BaseGrid. Скопировать сущность через контексное меню")]
    public void CopyEntityViaContextMenu()
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var action = ScContext.Get<string>(Context.Action);
      var card = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
      using (new TestLogs("Скопировать сущность через контексное меню", true))
      {
        grid.RunContextMenuCommand(action);
        if (!card.CardExists(5000))
          throw new WebGuiException("Карточка сущности не была открыта");
      }
    }

    [StepDefinition("BaseGrid. Выделить (.*) сущность")]
    [StepDefinition("BaseGrid. Выделить (.*) сущности")]
    [StepDefinition("BaseGrid. Выделить (.*) сущностей")]
    public void SelectMultipleEntities(string selectionsNumber)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs($"Выделить {selectionsNumber} сущности/сущностей", true))
      {
        if (!int.TryParse(selectionsNumber, out var num))
        {
          throw new ArgumentException("selectionsNumber: ожидалось int значение, не удалось преобразовать из string в int");
        }
        grid.SelectEntities(num);
      }
    }

    [StepDefinition("BaseGrid. Скопировать ссылки на выделенные сущности")]
    public void CopyLinksForSelectedEntites()
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs("Скопировать ссылки на выделенные сущности", true))
      {
        grid.CopySelectedEntities();
      }
    }

    [StepDefinition("BaseGrid. В списке (.*) (должен|должна|должно|должны) (присутствовать|отсутствовать) (.*)")]
    public void CheckThatGridHasEntities(string gridName, string shouldBe, string present, string entityName)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs($"В списке {gridName} {shouldBe} {present} {entityName}", true))
      {
        var column = ScContext.Get<string>(EntitiesSteps.Context.Column);
        var values = ScContext.Get<List<string>>(EntitiesSteps.Context.Values);

        foreach (var value in values)
          grid.HasEntity(value, column).Should().Be(present == "присутствовать", $"В списке {gridName} есть {entityName}");
      }
    }

    [StepDefinition("BaseGrid. Проверить, что правила настройки есть в списке (.*)")]
    public void CheckThatGridHasEntities(string gridName)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      using (new TestLogs($"Проверить, что правила настройки есть в списке {gridName}", true))
      {
        var values = ScContext.Get<List<string>>(EntitiesSteps.Context.Value);
        var checks = new List<bool>();
        foreach (var value in values)
        {
          checks.Add(grid.HasEntity(value, grid.DefaultPropertyName));
        }
        checks.Contains(false).Should().BeFalse();
      }
    }

    [StepDefinition("BaseGrid. Выбрать (.*) по имени")]
    [StepDefinition("BaseGrid. Выбрать несколько (.*) по имени")]
    public void SelectMultipleEntitiesByName(string r)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      grid.Refresh();
      var entityNames = ScContext.Get<List<string>>(EntitiesSteps.Context.EntityNames);
      using (new TestLogs($"Выбрать несколько {r} по имени", true))
        grid.SelectEntitiesByNames(entityNames);     
    }

    [StepDefinition("BaseGrid. Открыть панель управления доступом из списка")]
    public void OpenAccessRightsPanelFromList()
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      ScContext[Context.AccessRightsSideBar] = grid.OpenAccessRightsPanel(); 
    }

    [StepDefinition("BaseGrid. Создать копию сущности")]
    public void CreateCopy()
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var oldCard = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
      ScContext[CardPageSteps.Context.Card] = grid.Copy(oldCard.GetType());
    }

    [StepDefinition("BaseGrid. Открыть карточку в новой вкладке")]
    public void OpenCardInNewTab()
    {
      using (new TestLogs("Открыть карточку в новой вкладке.", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.RunContextMenuActionById(GridPage.ActionsId.OpenCardInNewTab);
      }
    }

    [StepDefinition("BaseGrid. Открыть настройку прав доступа")]
    public void OpenAccessRightsSetting()
    {
      using (new TestLogs("Открыть настройку прав доступа.", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var action = ScContext.Get<string>(Context.Action);
        grid.RunContextMenuCommand(action);
        var dialog = new MassAccessRightsDialog();
        var exists = dialog.Exists(5000);
        if (!exists)
          throw new WebGuiException("Диалог настройки прав доступа не открыт");

        ScContext[MassAccessRightsDialogSteps.Context.MassAccessRightsDialog] = dialog;
      }
    }

    [StepDefinition("BaseGrid. Установить значение в быстром фильтре: (.*)")]
    public void SetQuickFilter(string name)
    {
      using (new TestLogs($"Установить значение в быстром фильтре {name}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.SetQuickFilter(name);
      }
    }

    [StepDefinition("BaseGrid. Открыть список типов сущностей для создания из Контестного меню")]
    public void OpenContextMenu()
    {
      using (new TestLogs("Открыть список типов сущностей для создания из Контестного меню", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.OpenContextMenu();
      }
    }

    [StepDefinition("BaseGrid. Получить список типов сущностей для создания из Контестного меню")]
    public void GetCreateListForContextMenu()
    {
      using (new TestLogs("Открыть список типов сущностей для создания из Контестного меню", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        ScContext[Context.EntityCreationList] = grid.GetCreateListContextMenu();
      }
    }
    
    [StepDefinition("BaseGrid. Восстановить вид по умолчанию")]
    public void RestoreDefaultView()
    {
      using (new TestLogs("Восстановить вид по умолчанию", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.CloseHintIfExist();
        grid.RestoreDefaultView();
      }
    }

    [StepDefinition("^BaseGrid. Открыть выделенный документ в предпросмотре с ожиданием загрузки (true|false)$")]
    public void OpenDocumentPreview(bool waitLoad)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var actionType = ScContext.Get<ActionType>(Context.ActionType);
      var document = ScContext.Get<BaseDocument>(EntitiesSteps.Context.Entity);
      using (new TestLogs($"Открыть документ из списка в предпросмотре с ожиданием загрузки {waitLoad}.", true))
      {
        var editor = grid.ReadByPreview(actionType, document.Name, waitLoad);
        ScContext[PreviewSteps.Context.Editor] = editor;
        ScContext[PreviewSteps.Context.OpenContext] = ModalPreview.OpenPreviewContext.Grid;
      }
    }

    [StepDefinition("^BaseGrid. Открыть выделенный документ на редактирование из контекстного меню (локально|в онлайн редакторе)$")]
    public void OpenDocumentForEditing(string openingMethod)
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      var actionType = ScContext.Get<ActionType>(Context.ActionType);
      using (new TestLogs($"Открыть выделенный документ на редактирование из контекстного меню {openingMethod}.", true))
      {
        if (openingMethod.Equals("в онлайн редакторе"))
        {
          var editor = grid.EditSelectedDocument(actionType);
          ScContext[OnlineEditorSteps.Context.Editor] = editor;
        }
        else
        {
          ScContext[ExplorerSteps.Context.LocalEditDocumentsCount] = ExplorerPage.LocalEditIcon.Exists() ? 
            int.Parse(ExplorerPage.LocalEditDocumentsCount.Text) : 0;
          grid.EditSelectedDocumentLocallyByContextMenu();
          //проверяем, что появился процесс
          var extension = ScContext.Get<string>(BaseDocumentSteps.Context.DocumentExtension);
          if (!HostsManagementUtils.CheckDocumentOpenedLocally(extension))
            throw new APIException("Документ не был открыт локально");
        }
      }
    }

    [AfterScenario("AfterScenarioWithFilter")]
    [StepDefinition("BaseGrid. Очистить быстрый фильтр в списке")]
    public void ClearFilter()
    {
      var grid = ScContext.Get<GridPage>(Context.Grid);
      grid.ClearFilter();
    }

    [StepDefinition("BaseGrid. Открыть фильтр по колонке (.*)")]
    public void OpenColumnFilterPanel(string column)
    {
      using (new TestLogs($"Открыть фильтр по колонке {column}", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        grid.Refresh();
        ScContext[Context.ColumnFilterPanel] = grid.OpenColumnFilterPanel(column);
      }
    }

    [StepDefinition("ColumnFilterPanel. Заполнить критерии поиска: (.*)")]
    public void FillSearchCriteriaColumnFilterPanel(string criteria)
    {
      using (new TestLogs($"Заполнить критерии поиска: {criteria}", true))
      {
        var panel = ScContext.Get<ColumnFilterPanel>(Context.ColumnFilterPanel);
        panel.SetValue(ColumnFilterPanel.SearchAllRecords, criteria, false);
        ScContext[LikeSearchSteps.Context.Criteries] = criteria;
      }
    }

    [StepDefinition("BaseGrid. Открыть панель фильтрации")]
    public void OpenFilterPanel()
    {
      using (new TestLogs("Открыть панель фильтрации", true))
      {
        var grid = ScContext.Get<GridPage>(Context.Grid);
        var filterPanel = grid.OpenFilterPanel();
        ScContext[Context.FilterPanel] = filterPanel;
      }
    }

    #endregion

    #region Вложенный класс

    public static class Context
    {
      public const string Grid = "grid";

      public const string Title = "Context";

      public const string Action = "action";

      public const string ActionsPath = "ActionsPath";

      public const string ActionType = "actionType";

      public const string AccessRightsSideBar = "accessRightsSideBar";

      public const string EntityCreationList = "EntityCreationList";

      public const string ModalGrid = "ModalGrid";

      public const string RowsCount = "RowsCount";

      public const string ColumnFilterPanel = "ColumnFilterPanel";
      
      public const string FilterPanel = "FilterPanel";

      public const string Flag = "Flag";
    }

    #endregion

    #region Конструктор

    public GridPageSteps(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion
  }
}