using System.Collections.Generic;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using FluentAssertions;
using Sungero.AutotestObjects.Web.Dialogs;
using Reqnroll;
using static Sungero.AutotestObjects.Web.Dialogs.TaskDialog;

namespace Sungero.AutotestObjects.Web.SpecflowSteps
{
  [Binding]
  public class ErrorDialogSteps : WebSteps
  {
    [Then("TaskDialog. Должен отобразиться диалог с ошибкой (.*)")]
    public void ErrorShouldBeVisible(string text)
    {
      var errorMessage = ScContext.Get<List<string>>(TaskDialogSteps.Context.Message);

      using (new TestLogs($"Должен отобразиться диалог с ошибкой: {errorMessage}", true))
      {
        var dialog = new TaskDialog(DialogTypes.Error);
        if (!dialog.Exists(5000)) throw new WebGuiException("Диалог не открыт");
        var errorDescription = dialog.DialogDescription;
        dialog.OK();
        errorDescription.Should().Be(errorMessage[0], $"Отображается ошибка \"{errorMessage[0]}\"");
      }

      errorMessage.RemoveAt(0);

      ScContext[TaskDialogSteps.Context.Message] = errorMessage;
    }

    [Then("TaskDialog. Должен появиться диалог с ошибкой (.*)")]
    public void ErrorDialogBeVisible(string condidtion)
    {
      var dialog = new TaskDialog(DialogTypes.Error);
      using (new TestLogs($"Должен появиться диалог с ошибкой {condidtion}", true))
      {
        dialog.Exists(5000).Should().BeTrue();
        var errorDescription = dialog.DialogDescription;
        Logs.Info(errorDescription);
      }
      dialog.OK();
    }

    public ErrorDialogSteps(ScenarioContext scContext) : base(scContext) { }
  }
}
