using Autotest.Web.Utils;
using Sungero.AutotestObjects.Web.Controls;
using Sungero.AutotestObjects.Web.Core;
using Sungero.AutoTestObjects.SpecflowSteps;
using Reqnroll;

namespace Sungero.AutotestObjects.Web.SpecflowSteps
{
  [Binding]
  public class ModuleSteps : WebSteps
  {

    [StepDefinition(@"ModuleCover. Открыть список (.*) с обложки модуля")]
    public void OpenCoverAction(string entity)
    {
      using (new TestLogs($"Открыть список {entity} с обложки модуля.", true))
      {
        var coverAction = ScContext.Get<string>(EntitiesSteps.Context.Entity);
        ModulePage.RunCoverAction(coverAction, true);
        var modalGrid = ScContext.Get<GridPage>(GridPageSteps.Context.Grid);
        var exists = modalGrid.Exists(5000);
        if (!exists)
          throw new WebGuiException($"Модальное окно с {entity} не открылось");
      }
    }

    public ModuleSteps(ScenarioContext scContext) : base(scContext)
    {
    }
  }
}
