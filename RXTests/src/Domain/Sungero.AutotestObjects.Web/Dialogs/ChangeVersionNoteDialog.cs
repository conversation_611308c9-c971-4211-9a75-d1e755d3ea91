using Autotest.Base.Utils;
using Autotest.Web.Utils.Selenium;
using OpenQA.Selenium;
using Sungero.AutotestObjects.Web.Base;

namespace Sungero.AutotestObjects.Web.Dialogs
{
  public class ChangeVersionNoteDialog : ModalDialog
  {

    private IWebElement textAreaInput => this.Dialog.S(By.CssSelector("textarea"));

    public static string ChangeBtn = Constants.Culture == Constants.En ? "Change" : "Изменить";

    public void SetNewValue(string value)
    {
      this.textAreaInput.ClearText();
      this.textAreaInput.SendKeys(value);
    }

    public ChangeVersionNoteDialog(PageObject parent) : base(Constants.Culture == Constants.Ru ? "Изменить примечание к версии" : "Change Version Note")
    {
    }
  }
}
