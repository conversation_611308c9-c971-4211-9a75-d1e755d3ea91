using System.Collections.Generic;
using System.IO;
using Autotest.Base.Utils;

namespace Sungero.AutoTestObjects.Vulnerability.Utils
{
  public static class Trivy
  {
    private static string[] images;

    public static string[] Images
    {
      get
      {
        if (images != null) return images;
        var dockerResult = SSH.ExecuteSshCommand("docker images --format {{.Repository}}:{{.Tag}}");
        images = dockerResult.Result.Split('\n');
        return images;
      }
    }


    private static readonly List<string> reportFormats = new List<string> { "table", "json" };

    public static Models.Trivy.Report GetReport(string reportName)
    {
      var path = Path.Combine($"{Constants.Trivy.WindowsResultReport}{reportName}");
      var json = File.ReadAllText(path);
      var result = Newtonsoft.Json.JsonConvert.DeserializeObject<Models.Trivy.Report>(json);
      return result;
    }

    /// <summary>
    /// Запуск утилиты Trivy для сканирования docker-образа
    /// </summary>
    /// <param name="imageName">Имя docker-образа</param>
    /// <param name="reportName">Имя отчета, куда сохранить результаты сканирования</param>
    /// <param name="config">Путь до конфигурационного файла Trivy</param>
    public static void RunTrivy(string imageName, string reportName, string config = "")
    {
      foreach (var reportFormat in reportFormats)
      {
        string command;
        if (string.IsNullOrEmpty(config))
          command = reportFormat == "json"
            ? $"trivy image -f {reportFormat} -o {Constants.Trivy.LinuxResultReport}/resultReport.json --scanners vuln {imageName}"
            : $"trivy image -f {reportFormat} -o {Constants.Trivy.LinuxResultReport}/{reportName}.txt --scanners vuln {imageName}";
        else
          command = reportFormat == "json"
            ? $"trivy --config {config} image -f {reportFormat} -o {Constants.Trivy.LinuxResultReport}/resultReport.json --scanners vuln {imageName}"
            : $"trivy --config {config} image -f {reportFormat} -o {Constants.Trivy.LinuxResultReport}/customDB/{reportName}.txt --scanners vuln {imageName}";

        Logs.Info(command);
        var result = SSH.ExecuteSshCommand(command);
        if (!string.IsNullOrWhiteSpace(result.Result))
          Logs.Info(result.Result);
        if (reportFormat == "json")
        {
          var sourcePath = $"{Constants.Trivy.WindowsResultReport}/resultReport.json";
          var destinationPath = "";
          destinationPath = string.IsNullOrEmpty(config)
            ? $"{Constants.Trivy.WindowsResultReport}/json/{reportName}.json"
            : $"{Constants.Trivy.WindowsResultReport}/json/customDB/{reportName}.json";

          // Проверка и удаление файла назначения, если он существует
          if (File.Exists(destinationPath))
          {
            File.Delete(destinationPath);
            Wait.WaitFor(() => !File.Exists(destinationPath));
          }

          File.Copy(sourcePath, destinationPath);
        }
        else
        {
          var txtReportName = string.IsNullOrEmpty(config) ? 
            $"{Constants.Trivy.WindowsResultReport}{reportName}.txt" : 
            $@"{Constants.Trivy.WindowsResultReport}customDB\{reportName}.txt";
          using (var reader = new StreamReader(txtReportName))
          {
            var text = reader.ReadToEnd();
            Logs.Info(text);
          }
        }
      }
    }

    public static string GenerateSbom(string imageName, string reportName)
    {
      var reportFile = $"{Constants.Trivy.LinuxResultReport}/sbom/{reportName}.json";
      // Перед запуском генерации sbom необходимо изменить папку TMP
      // По умолчанию задана папка в которой может быть недостаточно места.
      var command = "export TMPDIR=./temp/ " +
                    $"&& syft {imageName} -o cyclonedx-json > {reportFile}";

      Logs.Info(command);
      var result = SSH.ExecuteSshCommand(command);
      if (!string.IsNullOrWhiteSpace(result.Result))
        Logs.Info(result.Result);
      return reportFile;
    }

    public static void RunTrivySbom(string sbomReport, string reportName)
    {
      foreach (var reportFormat in reportFormats)
      {
        var command = reportFormat == "json"
          ? $" trivy sbom {sbomReport} -f {reportFormat} -o {Constants.Trivy.LinuxResultReport}/resultReport.json "
          : $" trivy sbom {sbomReport} -f {reportFormat} -o {Constants.Trivy.LinuxResultReport}/sbom/{reportName}.txt";
        Logs.Info(command);
        var result = SSH.ExecuteSshCommand(command);
        if (!string.IsNullOrWhiteSpace(result.Result))
          Logs.Info(result.Result);
      }
    }
  }
}