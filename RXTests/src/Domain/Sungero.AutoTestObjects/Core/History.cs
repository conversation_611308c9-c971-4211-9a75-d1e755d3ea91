

namespace Sungero.AutoTestObjects.Core
{
  public class History
  {
    #region Поля и свойства

    /// <summary>
    /// ИД пользователя.
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// Действие.
    /// </summary>
    public string Action { get; set; }

    /// <summary>
    /// Комментарий.
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    /// Признак действия по замещению.
    /// </summary>
    public bool? IsSubstitute { get; set; }

    /// <summary>
    /// Номер версии документа.
    /// </summary>
    public int? VersionNumber { get; set; }

    /// <summary>
    /// Операция.
    /// </summary>
    public string Operation { get; set; }

    #endregion
  }

  public abstract class HistoryActions
  {
    public static readonly string Create = "Create";
    public static readonly string ChangeType = "ChangeType";
    public static readonly string Read = "Read";
    public static readonly string Update = "Update";
    public static readonly string Delete = "Delete";
    public static readonly string Manage = "Manage";
    public static readonly string Sign = "Sign";
    public static readonly string Authentication = "Authentication";
    public static readonly string Execute = "Execute";
    public static readonly string ChangeRelation = "ChangeRelation";
    public static readonly string ImportSign = "ImportSign";
    public static readonly string AddTimestamp = "AddTimestamp";
    public static readonly string AddEvidence = "AddEvidence";
    public static readonly string AddArcTimestamp = "AddArcTimestamp";
    public static readonly string ForceUnlock = "ForceUnlock";
  }
}
