using System;
using System.Collections.Generic;
using System.Linq;
using Sungero.AutoTestObjects.Base;
using Sungero.AutoTestObjects.Content;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutoTestObjectsRX.Company;
using Sungero.AutoTestObjectsRX.Meetings;
using Sungero.AutoTestObjects.Core;

namespace Sungero.AutoTestObjects.AT.Event
{
  public partial class ParamsBook : Databookentry
  {
    #region Поля и свойства

    public static Guid Guid = new Guid("db594145-22bc-46b9-b9f8-5be11ebcb64f");

    public override Guid EntityGuid => Guid;

    public new static readonly string AssemblyQualifiedName = "Sungero.Events.IParamsBook, Sungero.Domain.Interfaces";

    public override string AssemblyQualified => AssemblyQualifiedName;

    public static readonly string CollectionName = "IParamsBooks";

    public override string Collection => CollectionName;

    public static string DisplayName { get; } = 
      Resource.GetEntitySystemResource(AssemblyQualifiedName, "DisplayName");

    public static string CollectionDisplayName { get; } = 
      Resource.GetEntitySystemResource(AssemblyQualifiedName, "CollectionDisplayName");

    private string _name;
    public string Name => _name ?? (_name = Data.GetStringValue(PropertyNames.Name));

    private Employee _propertyMeetingUser;
    public Employee PropertyMeetingUser => _propertyMeetingUser ?? 
      (_propertyMeetingUser = new Employee(Data.GetEntityValue(PropertyNames.PropertyMeetingUser)));

    private Meeting _propertyMeeting;
    public Meeting PropertyMeeting => _propertyMeeting ?? 
      (_propertyMeeting = new Meeting(Data.GetEntityValue(PropertyNames.PropertyMeeting)));

    private double? _real;
    public double? Real => _real ?? (_real = Data.GetDoubleValue(PropertyNames.Real));

    private BaseDocument _boundDocument;
    public BaseDocument BoundDocument => _boundDocument ?? 
      (_boundDocument = new BaseDocument(Data.GetEntityValue(PropertyNames.BoundDocument)));

    private ParamsBook _afterSaveBook;
    public ParamsBook AfterSaveBook => _afterSaveBook ?? 
      (_afterSaveBook = new ParamsBook(Data.GetEntityValue(PropertyNames.AfterSaveBook)));

    private ParamsBook _propChangeBookWithSelectForm;
    public ParamsBook PropChangeBookWithSelectForm => _propChangeBookWithSelectForm ?? 
      (_propChangeBookWithSelectForm = new ParamsBook(Data.GetEntityValue(PropertyNames.PropChangeBookWithSelectForm)));

    private DateTime? _birthDay;
    public DateTime? BirthDay => _birthDay ?? (_birthDay = Data.GetDateTimeValue(PropertyNames.BirthDay));

    private double? _realCh;
    public double? RealCh => _realCh ?? (_realCh = Data.GetDoubleValue(PropertyNames.RealCh));

    private DateTime? _onlyDate;
    public DateTime? OnlyDate => _onlyDate ?? (_onlyDate = Data.GetDateTimeValue(PropertyNames.OnlyDate));

    private DateTime? _dateAndTime;
    public DateTime? DateAndTime => _dateAndTime ?? (_dateAndTime = Data.GetDateTimeValue(PropertyNames.DateAndTime));

    private DateTime? _yearAndMonth;
    public DateTime? YearAndMonth => _yearAndMonth ?? (_yearAndMonth = Data.GetDateTimeValue(PropertyNames.YearAndMonth));

    private DateTime? _onlyYear;
    public DateTime? OnlyYear => _onlyYear ?? (_onlyYear = Data.GetDateTimeValue(PropertyNames.OnlyYear));

    private ParamsBookCollectionProperties _collectionProperty;
    public ParamsBookCollectionProperties CollectionProperty
    {
      get
      {
        if (_collectionProperty != null && !_collectionProperty.Any()) return _collectionProperty;
        var tracks = Data.GetCollectionValue(PropertyNames.CollectionProperty);
        _collectionProperty = new ParamsBookCollectionProperties(this);
        foreach (var route in tracks)
          _collectionProperty.Add(new ParamsBookCollectionProperty(route));

        return _collectionProperty;
      }
    }

    private ParamsBookMeetingParticipants _meetingParticipants;
    public ParamsBookMeetingParticipants MeetingParticipants
    {
      get
      {
        if (_meetingParticipants != null && !_meetingParticipants.Any()) return _meetingParticipants;
        var tracks = Data.GetCollectionValue(PropertyNames.MeetingParticipants);
        _meetingParticipants = new ParamsBookMeetingParticipants(this);
        foreach (var route in tracks)
          _meetingParticipants.Add(new ParamsBookMeetingParticipant(route));

        return _meetingParticipants;
      }
    }

    private ParamsBookCollectionProperty1s _collectionProperty1;
    public ParamsBookCollectionProperty1s CollectionProperty1
    {
      get
      {
        if (_collectionProperty1 != null && !_collectionProperty1.Any()) return _collectionProperty1;
        var tracks = Data.GetCollectionValue(PropertyNames.CollectionProperty1);
        _collectionProperty1 = new ParamsBookCollectionProperty1s(this);
        foreach (var route in tracks)
          _collectionProperty1.Add(new ParamsBookCollectionProperty1(route));

        return _collectionProperty1;
      }
    }

    #endregion

    #region Конструкторы

    public ParamsBook(IDictionary<string, dynamic> data) => Data = data;

    public ParamsBook() => Data = new Dictionary<string, dynamic>();

    #endregion

    #region Вложенные классы

    public new class PropertyNames : Databookentry.PropertyNames
    {
      public static readonly string Name = "Name";
      public static readonly string PropertyMeetingUser = "PropertyMeetingUser";
      public static readonly string PropertyMeeting = "PropertyMeeting";
      public static readonly string Real = "Real";
      public static readonly string BoundDocument = "BoundDocument";
      public static readonly string AfterSaveBook = "AfterSaveBook";
      public static readonly string PropChangeBookWithSelectForm = "PropChangeBookWithSelectForm";
      public static readonly string BirthDay = "BirthDay";
      public static readonly string RealCh = "RealCh";
      public static readonly string OnlyDate = "OnlyDate";
      public static readonly string DateAndTime = "DateAndTime";
      public static readonly string YearAndMonth = "YearAndMonth";
      public static readonly string OnlyYear = "OnlyYear";
      public static readonly string CollectionProperty = "CollectionProperty";
      public static readonly string MeetingParticipants = "MeetingParticipants";
      public static readonly string CollectionProperty1 = "CollectionProperty1";
    }

    public new class LocalizedProperties : Databookentry.LocalizedProperties
    {
      private static readonly Lazy<string> name = 
        new Lazy<string>(() => Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_Name"));
      
      public static string Name => name.Value;

      private static readonly Lazy<string> propertyMeetingUser = 
        new Lazy<string>(() => Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_PropertyMeetingUser"));
      
      public static string PropertyMeetingUser => propertyMeetingUser.Value;

      private static readonly Lazy<string> propertyMeeting = 
        new Lazy<string>(() => Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_PropertyMeeting"));
      
      public static string PropertyMeeting => propertyMeeting.Value;

      private static readonly Lazy<string> real = 
        new Lazy<string>(() => Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_Real"));
      
      public static string Real => real.Value;

      public static string BoundDocument { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_BoundDocument");

      public static string AfterSaveBook { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_AfterSaveBook");

      public static string PropChangeBookWithSelectForm { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_PropChangeBookWithSelectForm");

      public static string BirthDay { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_BirthDay");

      public static string RealCh { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_RealCh");

      public static string OnlyDate { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_OnlyDate");

      public static string DateAndTime { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_DateAndTime");

      public static string YearAndMonth { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_YearAndMonth");

      public static string OnlyYear { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_OnlyYear");

      public static string CollectionProperty { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_CollectionProperty");

      public static string MeetingParticipants { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_MeetingParticipants");

      public static string CollectionProperty1 { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_CollectionProperty1");
    }

    public new class Actions : Databookentry.Actions
    {
      public static string WebDialog { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_WebDialog");

      public static string GetCache { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_GetCache");

      public static string ShowDialog { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_ShowDialog");

      public static string TestDialog { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_TestDialog");

      public static string MasterDialog { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_MasterDialog");

      public static string FileSelectDialog { get; } =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_FileSelectDialog");
    }

    public new class Resources : Databookentry.Resources
    {
      private static readonly Lazy<string> serverExceptionText = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "ServerExceptionText"));
      
      public static string ServerExceptionText => serverExceptionText.Value;

      private static readonly Lazy<string> detailedText = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DetailedText"));
      
      public static string DetailedText => detailedText.Value;

      private static readonly Lazy<string> clientExceptionText = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "ClientExceptionText"));
      
      public static string ClientExceptionText => clientExceptionText.Value;

      private static readonly Lazy<string> entityWithDropDownList = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithDropDownList"));
      
      public static string EntityWithDropDownList => entityWithDropDownList.Value;

      private static readonly Lazy<string> entityWithDropDownListAndSelectedValue = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithDropDownListAndSelectedValue"));
      
      public static string EntityWithDropDownListAndSelectedValue => entityWithDropDownListAndSelectedValue.Value;

      private static readonly Lazy<string> entityWithSelectFormList = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithSelectFormList"));
      
      public static string EntityWithSelectFormList => entityWithSelectFormList.Value;

      private static readonly Lazy<string> entityWithSelectFormListAndSelectedValue = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithSelectFormListAndSelectedValue"));
      
      public static string EntityWithSelectFormListAndSelectedValue => entityWithSelectFormListAndSelectedValue.Value;

      private static readonly Lazy<string> entityWithUserTree = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithUserTree"));
      
      public static string EntityWithUserTree => entityWithUserTree.Value;

      private static readonly Lazy<string> entityWithUserTreeAndSelectedValue = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithUserTreeAndSelectedValue"));
      
      public static string EntityWithUserTreeAndSelectedValue => entityWithUserTreeAndSelectedValue.Value;

      private static readonly Lazy<string> entityWithPropertyOverridesTheDefault = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "EntityWithPropertyOverridesTheDefault"));
      
      public static string EntityWithPropertyOverridesTheDefault => entityWithPropertyOverridesTheDefault.Value;

      private static readonly Lazy<string> doubleProperty = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DoubleProperty"));
      
      public static string DoubleProperty => doubleProperty.Value;

      private static readonly Lazy<string> dateTimeProperty = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DateTimeProperty"));
      
      public static string DateTimeProperty => dateTimeProperty.Value;

      private static readonly Lazy<string> infoHintOfChangedDoubleProperty = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "InfoHintOfChangedDoubleProperty"));
      
      public static string InfoHintOfChangedDoubleProperty => infoHintOfChangedDoubleProperty.Value;

      private static readonly Lazy<string> dialogBooleanControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogBooleanControl"));
      
      public static string DialogBooleanControl => dialogBooleanControl.Value;

      private static readonly Lazy<string> dialogDateTimeControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogDateTimeControl"));
      
      public static string DialogDateTimeControl => dialogDateTimeControl.Value;

      private static readonly Lazy<string> dialogDoubleControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogDoubleControl"));
      
      public static string DialogDoubleControl => dialogDoubleControl.Value;

      private static readonly Lazy<string> dialogIntegerControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogIntegerControl"));
      
      public static string DialogIntegerControl => dialogIntegerControl.Value;

      private static readonly Lazy<string> dialogSelectControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogSelectControl"));
      
      public static string DialogSelectControl => dialogSelectControl.Value;

      private static readonly Lazy<string> dialogSelectMany = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogSelectMany"));
      
      public static string DialogSelectMany => dialogSelectMany.Value;

      private static readonly Lazy<string> dialogFileSelect = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogFileSelect"));
      
      public static string DialogFileSelect => dialogFileSelect.Value;

      private static readonly Lazy<string> dialogHyperlinkToEntity = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHyperlinkToEntity"));
      
      public static string DialogHyperlinkToEntity => dialogHyperlinkToEntity.Value;

      private static readonly Lazy<string> dialogHyperlinkToList = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHyperlinkToList"));
      
      public static string DialogHyperlinkToList => dialogHyperlinkToList.Value;

      private static readonly Lazy<string> dialogHyperlinkToSite = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHyperlinkToSite"));
      
      public static string DialogHyperlinkToSite => dialogHyperlinkToSite.Value;

      private static readonly Lazy<string> dialogMultilineStringControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogMultilineStringControl"));
      
      public static string DialogMultilineStringControl => dialogMultilineStringControl.Value;

      private static readonly Lazy<string> dialogStringControl = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogStringControl"));
      
      public static string DialogStringControl => dialogStringControl.Value;

      private static readonly Lazy<string> dialogButtonNext = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogButtonNext"));
      
      public static string DialogButtonNext => dialogButtonNext.Value;

      private static readonly Lazy<string> dialogButtonPrevious = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogButtonPrevious"));
      
      public static string DialogButtonPrevious => dialogButtonPrevious.Value;

      private static readonly Lazy<string> dialogButtonReadOnly = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogButtonReadOnly"));
      
      public static string DialogButtonReadOnly => dialogButtonReadOnly.Value;

      private static readonly Lazy<string> dialogButtonOK = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogButtonOK"));
      
      public static string DialogButtonOK => dialogButtonOK.Value;

      private static readonly Lazy<string> chooseEntityFromList = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "ChooseEntityFromList"));
      
      public static string ChooseEntityFromList => chooseEntityFromList.Value;

      private static readonly Lazy<string> masterDialog = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "MasterDialog"));
      
      public static string MasterDialog => masterDialog.Value;

      private static readonly Lazy<string> dialogPageFirst = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogPageFirst"));
      
      public static string DialogPageFirst => dialogPageFirst.Value;

      private static readonly Lazy<string> dialogPageSecond = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogPageSecond"));
      
      public static string DialogPageSecond => dialogPageSecond.Value;

      private static readonly Lazy<string> dialogLabelString = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogLabelString"));
      
      public static string DialogLabelString => dialogLabelString.Value;

      private static readonly Lazy<string> dialogHintInformation = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHintInformation"));
      
      public static string DialogHintInformation => dialogHintInformation.Value;

      private static readonly Lazy<string> dialogHintWarning = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHintWarning"));
      
      public static string DialogHintWarning => dialogHintWarning.Value;

      private static readonly Lazy<string> dialogHintErrorComingDay = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHintErrorComingDay"));
      
      public static string DialogHintErrorComingDay => dialogHintErrorComingDay.Value;

      private static readonly Lazy<string> fileSelectDialog = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "FileSelectDialog"));
      
      public static string FileSelectDialog => fileSelectDialog.Value;

      private static readonly Lazy<string> dialogHyperlinkToDialog = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogHyperlinkToDialog"));
      
      public static string DialogHyperlinkToDialog => dialogHyperlinkToDialog.Value;

      private static readonly Lazy<string> dialogFromDialog = 
        new Lazy<string>(() => Resource.GetEntityResource(AssemblyQualifiedName, "DialogFromDialog"));
      
      public static string DialogFromDialog => dialogFromDialog.Value;
    }

    #endregion
  }
}