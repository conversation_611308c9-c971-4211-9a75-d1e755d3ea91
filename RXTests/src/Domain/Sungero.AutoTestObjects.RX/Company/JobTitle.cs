using Autotest.Base.Utils;
using Sungero.AutoTestObjects.Base;
using System.Linq;

namespace Sungero.AutoTestObjectsRX.Company
{
  public class JobTitle : Databookentry
  {
    #region Поля и свойства

    public static readonly string CollectionName = "IJobTitles";

    private new static string AssemblyQualifiedName => "Sungero.Company.IJobTitle, Sungero.Domain.Interfaces";

    public override string Collection => CollectionName;

    public override string AssemblyQualified => AssemblyQualifiedName;

    #endregion

    #region Методы

    /// <summary>
    /// Получить должность по имени.
    /// </summary>
    /// <returns>Должность, если её удалось найти. Иначе - null.</returns>
    public static JobTitle GetJobTitle(string jobTitle)
    {
      var filter = $"{Databookentry.PropertyNames.Name} eq '{jobTitle}'";
      var result = GetWithAllProperties<JobTitle>(filter).FirstOrDefault();

      if (result == null)
        throw new APIException($"Должность с именем {jobTitle} не существует");

      return result;
    }

    #endregion

    #region Вложенные классы

    public class Entities
    {
      public static string ChiefExpert => "Главный эксперт";
      public static string SalesManager => "Менеджер по продажам(Функ)";
    }

    public new class PropertyNames : Databookentry.PropertyNames
    {
      public static readonly string Department = "Department";
    }

    #endregion
  }
}