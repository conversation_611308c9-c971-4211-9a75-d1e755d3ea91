using System.Collections.Generic;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutoTestObjects.WF;

namespace Sungero.AutoTestObjectsRX.Exchange
{
  public class ExchangeIncomingInvitationAssignment : Assignment
  {
    private new static readonly string AssemblyQualifiedName = "Sungero.ExchangeCore.IIncomingInvitationAssignment, Sungero.Domain.Interfaces";

    public new static string DisplayName { get; } =  Resource.GetEntitySystemResource(AssemblyQualifiedName, "DisplayName");

    public new class Actions : Assignment.Actions
    {
      public static string Accept =>
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_Accept");

      public static string Reject =>
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_Reject");
    }

    public ExchangeIncomingInvitationAssignment(IDictionary<string, dynamic> data) : base(data)
    {
    }
  }
}