using System;
using System.Collections.Generic;
using Sungero.AutoTestObjects.Base;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutoTestObjects.WF;
using Settings = Autotest.Base.Utils.Constants;

namespace Sungero.AutoTestObjectsRX.DocflowApproval
{
  public class EntityReworkAssignment : Assignment
  {
    #region Свойства
    
    public new static string CollectionName = "IEntityReworkAssignments";

    public override string Collection => CollectionName;
    
    private new static string AssemblyQualifiedName => "Sungero.DocflowApproval.IEntityReworkAssignment, Sungero.Domain.Interfaces";
    
    public new static string DisplayName { get; } =
      Resource.GetEntitySystemResource(AssemblyQualifiedName, "DisplayName");

    private static Guid Guid = new Guid("14d47b91-96e5-4b5f-89c0-8f8a8db7bc4c");
    
    public override Guid EntityGuid => Guid;

    public static string ReworkSubjectFormat =
      Settings.Culture == Settings.Ru ? "Доработайте: {0}" : "Make revisions to {0}";
    
    private IEnumerable<IDictionary<string, dynamic>> approvers;
    public IEnumerable<IDictionary<string, dynamic>> Approvers => 
      approvers ?? 
      (approvers = Data.GetCollectionValue(EntityReworkAssignment.PropertyNames.Approvers));
    
    #endregion

    #region Вложенные классы

    public new class Actions : Assignment.Actions
    {
      public static string ForReapproval =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Action_ForReapproval");
    }

    public new class LocalizedProperties : Assignment.LocalizedProperties
    {
      public static string NewDeadline =
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_NewDeadline");
      
      public static string Approvers = 
        Resource.GetEntitySystemResource(AssemblyQualifiedName, "Property_Approvers");
      
      private static readonly string AssemblyQualifiedNameApprovers =
        "Sungero.DocflowApproval.IEntityReworkAssignmentApprovers, Sungero.Domain.Interfaces";

      public static readonly string Approver =
        Resource.GetEntitySystemResource(AssemblyQualifiedNameApprovers, "Property_Approver");

      public static readonly string Action =
        Resource.GetEntitySystemResource(AssemblyQualifiedNameApprovers, "Property_Action");
    }
    
    public new class PropertyNames : Assignment.PropertyNames
    {
      public static readonly string Approvers = "Approvers";

      public static readonly string ApproversApprover = "Approver";
      
      public static readonly string NewDeadline = "NewDeadline";
    }
    
    public new class ActionEnum
    {
      public static readonly string SendForApproval =
        Settings.Culture == Settings.Ru ? "Отправить на согласование" : "Send for approval";

      public static readonly string SendNotice =
        Settings.Culture == Settings.Ru ? "Отправить уведомление" : "Send notice";
    }
    
    #endregion
    
    public EntityReworkAssignment(IDictionary<string, dynamic> data) : base(data) { }

    public EntityReworkAssignment() : base(new Dictionary<string, dynamic>()) { }
  }
}