using System;
using System.Collections.Generic;
using Sungero.AutoTestObjects.Base;
using Sungero.AutotestObjects.Web.Core;

namespace Sungero.AutoTestObjects.Targets.Web.Grids
{
  public class TargetsMapGrid : ListGrid
  {
    #region Поля и свойства

    public override List<string> DefaultVisibleColumn => new List<string>
    {
      Targets.TargetsMap.LocalizedProperties.Code,
      Targets.TargetsMap.LocalizedProperties.Name,
      Targets.TargetsMap.LocalizedProperties.Period,
      Databookentry.LocalizedProperties.Status
    };

    public override string DefaultPropertyName => Targets.TargetsMap.LocalizedProperties.Name;

    public override Guid Metadata => Targets.TargetsMap.Guid;

    protected override string caption => Targets.TargetsMap.CollectionDisplayName;

    #endregion
  }
}