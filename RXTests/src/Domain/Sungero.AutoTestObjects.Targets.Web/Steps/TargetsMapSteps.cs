using Autotest.Web.Utils;
using FluentAssertions;
using Reqnroll;
using Sungero.AutoTestObjects.Targets.Targets.Steps;
using Sungero.AutoTestObjects.Targets.Web.Cards;
using Sungero.AutoTestObjects.Targets.Web.Controls;
using Sungero.AutotestObjects.Web;
using Sungero.AutotestObjects.Web.SpecflowSteps;

namespace Sungero.AutoTestObjects.Targets.Web.Steps
{
  [Binding]
  public class TargetsMapSteps : WebSteps
  {
    #region Steps

    [StepDefinition("^TargetsMap. Создать (основную|подчинённую) цель$")]
    public void CreateGoal(string goalLead)
    {
      using (new TestLogs($"Создать {goalLead} цель", true))
      {
        var card = ScContext.Get<TargetsMapCard>(CardPageSteps.Context.Card);
        GoalInput goal = null;

        switch (goalLead)
        {
          case "основную":
            goal = card.TargetsMap.CreateGoal();
            break;
          case "подчинённую":
            var leadingTargetName = ScContext.Get<string>(TargetSteps.Context.LeadingTargetName);
            goal = card.TargetsMap.CreateSubordinateGoal(leadingTargetName);
            break;
        }

        ScContext.Set(goal);
      }
    }

    [StepDefinition("TargetsMap. Открыть карточку цели")]
    public void OpenGoalCard()
    {
      using (new TestLogs("Открыть карточку цели", true))
      {
        var card = ScContext.Get<TargetsMapCard>(CardPageSteps.Context.Card);
        var goalName = ScContext.Get<string>(GoalInputSteps.Context.GoalName);
        var goalCard = card.TargetsMap.OpenGoalCard(goalName);

        ScContext[CardPageSteps.Context.Card] = goalCard;
      }
    }

    #endregion

    #region Then

    [Then("TargetsMap. Цель должна отображаться на карте")]
    public void CheckGoal()
    {
      using (new TestLogs("Цель должна отображаться на карте", true))
      {
        var card = ScContext.Get<TargetsMapCard>(CardPageSteps.Context.Card);
        var goalName = ScContext.Get<string>(GoalInputSteps.Context.GoalName);

        card.TargetsMap.GoalExists(goalName).Should().BeTrue("Цель должна отображаться на карте");
      }
    }

    #endregion

    #region Конструктор

    public TargetsMapSteps(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion
  }
}