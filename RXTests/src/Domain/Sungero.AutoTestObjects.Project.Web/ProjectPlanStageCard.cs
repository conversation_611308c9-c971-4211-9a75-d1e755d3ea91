using System;
using System.Linq;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using Autotest.Web.Utils.Selenium;
using OpenQA.Selenium;
using Sungero.AutotestObjects.Web.Base;
using Sungero.AutotestObjects.Web.Core;
using Sungero.AutoTestObjects.Utils;

namespace Sungero.AutoTestObjects.Project.Web
{
  public class ProjectPlanStageCard
  {

    #region Поля и свойства

    private WebDriver driver => DriversManager.Current;
    private const string changeActivityTypeButtonSel = "select_type";
    private const string activityTypesMenuSelector = "[class*=\"type-picker_panel\"][class*=\"base-popup_options\"]";
    private const string activityTypesListSel = "[class*='list_item']";
    private const string activityNameInputSel = "[class*=\"input lightbox-header\"]";
    private const string saveActivityButtonSel = "lightbox_save_button";
    private const string performerInputSel = "[class*=\"search-entity_input_\"]";
    private const string performerSelectOptionSel = "[class*=\"user_container\"]>[title*=\"{0}\"]";
    private const string addPerformerButtonSel = "add_performer_button";
    private const string deletePerformerButtonSel = "[id*=\"delete_performer\"]";
    private const string closeBtnSel = "[class*=\"closeButton\"]";
    private const string addRiskButtonSel = "risks_editor_button";
    private const string searchRiskInputSel = "[class*=\"search-entity_input__\"]";
    private const string createRiskButtonSel = "create_risk_button";
    private const string addGateButtonSel = "add_gate_button";
    private const string firstLevelGateItemSel = "create_gate_button_Key";

    public static string Milestone => Constants.Culture == Constants.Ru ? "Веха" : "Milestone";
    public static string Operation => Constants.Culture == Constants.Ru ? "Фин. операция" : "Fin. operation";

    #endregion

    #region Методы

    public RiskCard CreateRiskFromLightBox()
    {
      var addRiskButton = this.driver.S(By.Id(addRiskButtonSel));
      addRiskButton.ClickElement();
      var searchRiskInput = this.driver.S(By.CssSelector(searchRiskInputSel), TimeSpan.FromSeconds(5));
      var riskName = TestDataGenerator.Faker.Lorem.Sentence(1);
      searchRiskInput.SendKeys(riskName);
      var createRiskButton = this.driver.S(By.Id(createRiskButtonSel));

      var tabsCount = this.driver.WindowHandles.Count;
      createRiskButton.ClickElement();
      driver.ActivateTabAndWait(tabsCount + 1);
      var card = new RiskCard(DisplayMode.Satelite) { Name = riskName };

      if (!card.CardExists())
        throw new WebGuiException("Не удалось открыть карточку риска.");

      return card;
    }

    private void ChangeType(string typeName)
    {
      var changeActivityTypeButton = this.driver.S(By.Id(changeActivityTypeButtonSel));
      changeActivityTypeButton.ClickElement();
      //TODO Kiselev_EM: Посмотреть, почему бы сразу не селектить activityTypesList?
      var activityTypesMenu = this.driver.S(By.CssSelector(activityTypesMenuSelector));
      var activityTypesList = activityTypesMenu.SS(By.CssSelector(activityTypesListSel));
      var targetType = activityTypesList
        .FirstOrDefault(x => x.Text.Equals(typeName));
      targetType.Scroll();
      targetType.Click();
    }

    public void ChangeActivityTypeToFinOperation() => this.ChangeType(Operation);
    public void ChangeActivityTypeToMilestone() => this.ChangeType(Milestone);
    public void ChangeActivityTypeToCustom(string typeName) => this.ChangeType(typeName);

    public void FillActivityName(string activityName)
    {
      var activityNameInput = this.driver.S(By.CssSelector(activityNameInputSel), TimeSpan.FromSeconds(5));
      if (activityNameInput == null)
      {
        throw new WebGuiException("Не найдено поле ввода имени этапа.");
      }

      activityNameInput.Clear();
      activityNameInput.SendKeys(activityName);
    }

    public void SaveActivity()
    {
      var saveActivityButton = this.driver.S(By.Id(saveActivityButtonSel), TimeSpan.FromSeconds(5));

      if (saveActivityButton == null) throw new WebGuiException("Не найдена кнопка сохранения этапа.");

      saveActivityButton.ClickElement();
    }

    public void CloseActivity()
    {
      var closeBtn = this.driver.S(By.CssSelector(closeBtnSel));
      closeBtn.Click();
    }

    public void AddPerformer(string performerName)
    {
      var addPerformerButton = this.driver.S(By.Id(addPerformerButtonSel));
      addPerformerButton.ClickElement();

      var performerInput = this.driver.S(By.CssSelector(performerInputSel), TimeSpan.FromSeconds(5));
      if (performerInput == null)
      {
        throw new WebGuiException("Не найдено поле ввода ответственного.");
      }

      performerInput.SendKeys(performerName);

      var resultLocator = string.Format(performerSelectOptionSel, performerName);
      var performerSelectOption = this.driver.S(By.CssSelector(resultLocator), TimeSpan.FromSeconds(5));
      if (performerSelectOption == null)
        throw new WebGuiException($"Не удалось найти сотрудника {performerName}.");

      performerSelectOption.ClickElement();
    }

    public void DeletePerformer()
    {
      var performer = this.driver.S(By.CssSelector("[class*=\"performer-view_performer\"]"));
      performer.HoverToElement();

      var deletePerformerButton = performer.S(By.CssSelector(deletePerformerButtonSel));
      if (!deletePerformerButton.Exists()) throw new WebGuiException($"Не найдена кнопка удаления исполнителя.");

      deletePerformerButton.HoverToElement();
      BaseUtils.WaitFor(() => deletePerformerButton.GetDomAttribute("allowdelete") != null, maxAttempts: 5);
      deletePerformerButton.ClickElement();
    }

    public static void WaitIsLoadData()
    {
      PageObject.WaitByLocatorIsLoad(By.CssSelector("loader-module_holder"), true, TimeSpan.FromSeconds(10));
    }

    public void CreateActivityGate()
    {
      var addGateButton = this.driver.S(By.Id(addGateButtonSel));
      addGateButton.ClickElement();
      var firstLevelGateItem = this.driver.S(By.Id(firstLevelGateItemSel));
      firstLevelGateItem.ClickElement();
      WaitIsLoadData();
    }

    #endregion

  }
}