using Autotest.DDS.Utils;
using Autotest.Web.Utils;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutoTestObjectsDSDesktop.Controls;
using Sungero.AutoTestObjectsDSDesktop.Controls.Properties;
using Sungero.AutoTestObjectsDSDesktop.Editors.Core;
using System;
using System.Collections.Generic;

namespace Sungero.AutoTestObjectsDSDesktop.Editors.Entity
{
  /// <summary>
  /// Редактор действия
  /// </summary>
  public class EntityActionEditor : ItemEditorBase
  {
    #region Константы

    // Панель Именование

    public const string DisplayName = nameof(DisplayName);

    public const string Description = nameof(Description);

    // Панель Параметры

    public const string ActionArea = nameof(ActionArea);

    public const string СollectionProperty = nameof(СollectionProperty);

    public const string ConfirmationMessage = nameof(ConfirmationMessage);

    #endregion

    #region Поля и свойства

    // Панель Параметры

    public Lazy<BooleanControl> NeedConfirmation { get; }

    public readonly Dictionary<string, string> collectionPropertiesTestIdPostfix = new Dictionary<string, string>
    {
      { "DataBookCollection", DDSConstants.TestEntityIds.DataBookCollectionId },
      { "TestCollection", DDSConstants.TestEntityIds.TestCollectionId }
    };

    public readonly Dictionary<string, string> actionAreaTypesTestIdPostfix = new Dictionary<string, string>
    {
      { "Карточка", "Card" },
      { "Список", "Collection" },
      { "Карточка и список", "CardAndCollection" },
      { "Дочерняя коллекция", "ChildCollection" }
    };

    // Панель Обработчики

    public readonly Dictionary<string, string> handlerTestIdPostfix = new Dictionary<string, string>
    {
      { "Выполнение", "executeLink" },
      { "Возможность выполнения", "canExecuteLink" }
    };

    #endregion

    #region Методы

    public void FillSimpleParameter(string parameterName, string value)
    {
      this.SimpleParameters[parameterName].Value.SetValue(value);
      this.WaitEndRequestOperation(TimeSpan.FromMilliseconds(200));
    }

    public LinkToActionHandlers CreateHandlers()
    {
      var handlerTestId = $"{testId}_actionHandlers";
      var actionHandler = new LinkToActionHandlers(this.EditorElement, handlerTestId);
      actionHandler.ClickCreateButton();
      return actionHandler;
    }

    public LinkToActionHandlers GetHandlers()
    {
      var handlerTestId = $"{testId}_actionHandlers";
      var actionHandler = new LinkToActionHandlers(this.EditorElement, handlerTestId);
      return actionHandler;
    }

    public void ClickNeedConfirmation()
    {
      this.NeedConfirmation.Value.Click();
      this.WaitEndRequestOperation(TimeSpan.FromMilliseconds(200));
      var confirmationMessageControl = this.SimpleParameters[ConfirmationMessage].Value as LocalizationControl;
      var isEditable = BaseUtils.WaitFor(() => confirmationMessageControl.IsEditable());
      if (!isEditable)
        throw new WebGuiException("Параметр 'Сообщение подтверждения' недоступен для редактирования");
    }

    public void WaitForShow()
    {
      _ = this.EditorElement ?? throw new WebGuiException("Редактора действия нет");
    }

    #endregion

    #region Конструкторы

    public EntityActionEditor()
    {
      this.SimpleParameters.Add(DisplayName, new Lazy<BaseControl>(() => new LocalizationControl(this.EditorElement, $"{testId}_param_displayName_input")));
      this.SimpleParameters.Add(Description, new Lazy<BaseControl>(() => new LocalizationControl(this.EditorElement, $"{testId}_param_description_input")));
      this.SimpleParameters.Add(ActionArea, new Lazy<BaseControl>(() => new DropDownControl(this.EditorElement, $"{testId}_param_actionArea_input", this.actionAreaTypesTestIdPostfix)));
      this.SimpleParameters.Add(СollectionProperty, new Lazy<BaseControl>(() => new DropDownControl(this.EditorElement, $"{testId}_param_collectionPropertyGuid_input", this.collectionPropertiesTestIdPostfix)));
      this.SimpleParameters.Add(ConfirmationMessage, new Lazy<BaseControl>(() => new LocalizationControl(this.EditorElement, $"{testId}_param_confirmationMessage_input")));
      this.NeedConfirmation = new Lazy<BooleanControl>(() => new BooleanControl(this.EditorElement, $"{testId}_param_needConfirmation_input"));
    }

    #endregion
  }
}
