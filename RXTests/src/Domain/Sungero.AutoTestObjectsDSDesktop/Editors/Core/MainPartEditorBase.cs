using Autotest.Web.Utils;
using Autotest.Web.Utils.Selenium;
using OpenQA.Selenium;
using Sungero.AutotestObjects.Web.Utils;
using Sungero.AutoTestObjectsDSDesktop.Controls;
using Sungero.AutoTestObjectsDSDesktop.Controls.Properties;
using System;
using System.Collections.Generic;

namespace Sungero.AutoTestObjectsDSDesktop.Editors.Core
{
  public class MainPartEditorBase : PartEditorBase
  {
    #region Константы

    protected const string testId = "metadataPartEditor";

    // Панель Именование

    public const string Name = nameof(Name);

    public const string Code = nameof(Code);

    #endregion

    #region Поля и свойства

    private By EditorLocator => ByExtensions.TestId(testId);

    public IWebElement Editor => this.driver.S(this.EditorLocator, TimeSpan.FromSeconds(20));

    public IDictionary<string, Lazy<BaseControl>> SimpleParameters { get; } = new Dictionary<string, Lazy<BaseControl>>();

    // Панель Функции

    public readonly Dictionary<string, string> functionTestIdPostfix = new Dictionary<string, string>
    {
      { "клиентскую", "functions_client" },
      { "серверную", "functions_server" },
      { "разделяемую", "functions_shared" },
      { "инициализации", "functions_initialization" }
    };

    #endregion

    #region Методы

    public bool IsEditorOpen(string entityName)
    {
      var nameValue = this.SimpleParameters[Name].Value.GetValue();
      return entityName == nameValue;
    }

    public override void WaitForShow()
    {
      _ = this.Editor ?? throw new WebGuiException("Редактор не был показан");
    }

    public virtual void FillParameter(string parameterName, string value)
    {
      this.SimpleParameters[parameterName].Value.SetValue(value);
      this.WaitEndRequestOperation(TimeSpan.FromMilliseconds(200));
    }

    public void ScrollToElement(IWebElement element)
    {
      this.driver.ExecuteScript("arguments[0].scrollIntoView({ behavior: 'smooth', block: 'center' });", element);
    }

    public void AddFunction(string functionType)
    {
      var functionTestId = $"{testId}_{functionTestIdPostfix[functionType]}";
      var function = new LinkToFunctions(this.Editor, functionTestId);
      function.ClickCreateButton();
    }

    #endregion

    #region Конструкторы

    public MainPartEditorBase()
    {
      this.SimpleParameters.Add(Name, new Lazy<BaseControl>(() => new StringControl(this.Editor, $"{testId}_param_name_input")));
      this.SimpleParameters.Add(Code, new Lazy<BaseControl>(() => new StringControl(this.Editor, $"{testId}_param_code_input")));
    }

    #endregion
  }
}
