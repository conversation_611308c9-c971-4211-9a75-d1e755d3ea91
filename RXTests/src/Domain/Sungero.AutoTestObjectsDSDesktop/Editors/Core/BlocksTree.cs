using Autotest.Web.Utils;
using Autotest.Web.Utils.Selenium;
using Sungero.AutotestObjects.Web.Utils;
using Sungero.AutoTestObjectsDSDesktop.Controls;
using System;
using System.Linq;

namespace Sungero.AutoTestObjectsDSDesktop.Editors.Core
{
  /// <summary>
  /// Дерево блоков
  /// </summary>
  public class BlocksTree : ItemsTreeBase
  {
    #region Константы

    public const string PropertiesNodeTestId = "Properties";

    public const string OutPropertiesNodeTestId = "OutProperties";

    #endregion

    #region Поля и свойства

    public override string testId => "metadataEditorBlocksList";

    #endregion

    #region Методы

    private ContextMenu ShowCreateContextMenu()
    {
      var createButton = new Button(this.Tree, $"{this.testId}_title_createButton");
      createButton.Click();

      var createMenu = new ContextMenu($"{this.testId}_menu");

      return createMenu;
    }

    public void ExpandBlockPropertiesItem(string blockName)
    {
      this.ExpandItem(blockName);
      this.ExpandItem($"{blockName}_{PropertiesNodeTestId}_{blockName}");
    }

    public void ExpandBlockOutPropertiesItem(string blockName)
    {
      this.ExpandItem(blockName);
      this.ExpandItem($"{blockName}_{OutPropertiesNodeTestId}_{blockName}"); // Bug427393 
    }

    public BlockPropertyEditor ClickProperty(string blockName, string propertyName)
    {
      var property = this.Tree.S(ByExtensions.TestId($"{this.testId}_{PropertiesNodeTestId}_{blockName}_{propertyName}"));
      if (property == null)
        throw new WebGuiException($"Cвойство {propertyName} блока {blockName} в дереве не найдено");
      property.ClickElement();

      return new BlockPropertyEditor();
    }

    public BlockPropertyEditor ClickOutProperty(string blockName, string outPropertyName)
    {
      var property = this.Tree.S(ByExtensions.TestId($"{this.testId}_{OutPropertiesNodeTestId}_{blockName}_{outPropertyName}"));
      if (property == null)
        throw new WebGuiException($"Выходное свойство {outPropertyName} блока {blockName} в дереве не найдено");
      property.ClickElement();

      return new BlockPropertyEditor();
    }

    public bool IsOutPropertyInTree(string blockName, string propertyName)
    {
      var property = this.Tree.S(ByExtensions.TestId($"{this.testId}_{OutPropertiesNodeTestId}_{blockName}_{propertyName}"), TimeSpan.Zero);
      return property != null;
    }

    public BlockEditorBase CreateBlock(string blockType)
    {
      var blocksBeforeAdd = GetItemNames();

      var contextMenu = this.ShowCreateContextMenu();
      contextMenu.SelectItem(blockType);
      this.WaitEndRequestOperation(TimeSpan.FromMilliseconds(200));

      var blocksAfterAdd = GetItemNames();

      if (blocksBeforeAdd.Count == blocksAfterAdd.Count)
        throw new WebGuiException("Блок не создан.");

      var newBlockName = blocksAfterAdd.Except(blocksBeforeAdd).FirstOrDefault();

      var editor = new BlockEditorBase();
      editor.WaitForShowNewEditor(newBlockName);

      return editor;
    }

    #endregion
  }
}
