using Autotest.Web.Utils.Selenium;
using OpenQA.Selenium;

namespace Sungero.AutoTestObjectsDSDesktop.Controls.Properties
{
  /// <summary>
  /// Контрол времени
  /// </summary>
  public class TimeControl : BaseControl
  {
    #region Методы

    public override void SetValue(string text)
    {
      var input = this.Current;
      input.ClearText();
      input.SendKeys(text + Keys.Tab);
    }

    public override string GetValue()
    {
      return this.Current.GetAttribute("value");
    }

    #endregion

    #region Конструкторы

    public TimeControl(IWebElement parent, string testId) : base(parent, testId)
    {
    }

    #endregion
  }
}