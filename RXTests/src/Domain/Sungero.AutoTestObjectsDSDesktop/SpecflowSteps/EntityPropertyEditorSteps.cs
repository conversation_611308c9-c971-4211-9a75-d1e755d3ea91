using Autotest.DDS.Utils;
using Autotest.Web.Utils;
using FluentAssertions;
using Reqnroll;
using Sungero.AutotestObjects.Web;
using Sungero.AutoTestObjectsDSDesktop.Controls;
using Sungero.AutoTestObjectsDSDesktop.Editors.Entity;
using System.Collections.Generic;
using System.Linq;

namespace Sungero.AutoTestObjectsDSDesktop.SpecflowSteps
{
  [Binding]
  public class EntityPropertyEditorSteps : WebSteps
  {
    #region Steps

    [StepDefinition("^Редактор свойства. (Заполнить|Изменить) Имя(| невалидным значением)$")]
    public void WhenFillName(string typeAction, string invalidName = null)
    {
      using (new TestLogs($"Редактор свойства. {typeAction} Имя{invalidName}", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var propertyType = ScContext.Get<string>(EntityPropertiesTreeSteps.Context.MetadataItemType);

        var propertyName = string.Empty;
        if (typeAction == "Изменить")
          propertyName = ScContext.Get<string>(EntityPropertyEditorSteps.Context.ChangedPropertyValue);
        else
          propertyName = ScContext.Get<string>(EntityPropertyEditorSteps.Context.PropertyName);

        if (propertyType == DDSConstants.MetadataItemTypes.Property)
          editor.FillName(propertyName, invalidName == string.Empty);
        else
        {
          var collectionName = ScContext.Get<string>(EntityPropertiesTreeSteps.Context.CollectionName);
          editor.FillName(propertyName, collectionName);
        }
      }
    }

    [When("^Редактор свойства. (Заполнить|Изменить) Имя в базе данных$")]
    public void WhenFillCode(string typeAction)
    {
      using (new TestLogs($"Редактор свойства. {typeAction} Имя в базе данных", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();

        var propertyCode = string.Empty;
        if (typeAction == "Изменить")
          propertyCode = ScContext.Get<string>(EntityPropertyEditorSteps.Context.ChangedPropertyValue);
        else
          propertyCode = ScContext.Get<string>(EntityPropertyEditorSteps.Context.PropertyName);

        editor.FillSimpleParameter(EntityPropertyEditor.Code, propertyCode);
      }
    }

    [StepDefinition("^Редактор свойства. (Заполнить|Изменить) Отображаемое имя$")]
    public void WhenFillDisplayName(string typeAction)
    {
      using (new TestLogs($"Редактор свойства. {typeAction} Отображаемое имя", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();

        var propertyDisplayName = string.Empty;
        if (typeAction == "Изменить")
          propertyDisplayName = ScContext.Get<string>(EntityPropertyEditorSteps.Context.ChangedPropertyValue);
        else
          propertyDisplayName = ScContext.Get<string>(EntityPropertyEditorSteps.Context.PropertyName);

        editor.FillSimpleParameter(EntityPropertyEditor.DisplayName, propertyDisplayName);
      }
    }

    [When("^Редактор свойства. (Заполнить|Изменить) Тип свойства - (.*)")]
    public void WhenFillPropertyType(string actionType, string propertyType)
    {
      using (new TestLogs($"Редактор свойства. {actionType} Тип свойства - {propertyType}", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var propertyTypeValue = actionType.Equals("Заполнить")
         ? ScContext.Get<string>(Context.PropertyType)
         : ScContext.Get<string>(Context.ChangedPropertyValue); 

        editor.FillSimpleParameter(EntityPropertyEditor.PropertyType, propertyTypeValue);
      }
    }

    [When("Редактор свойства. Заполнить Тип сущности - (.*)")]
    public void WhenFillEntityType(string entityType)
    {
      using (new TestLogs($"Редактор свойства. Заполнить Тип сущности - {entityType}", true))
      {
        var entityFullName = ScContext.Get<string>(Context.EntityFullName);
        var editor = ScContext.Get<EntityPropertyEditor>();
        editor.FillSimpleParameter(EntityPropertyEditor.EntityType, entityFullName);
      }
    }

    [StepDefinition("^Редактор свойства. Создать значение перечисления(| с невалидным именем)$")]
    public void WhenCreateEnumValue(string invalidName)
    {
      using (new TestLogs($"Редактор свойства. Создать значение перечисления{invalidName}", true))
      {
        var enumValues = ScContext.Get<Dictionary<string, string>>(Context.EnumValues);
        var editor = ScContext.Get<EntityPropertyEditor>();
        foreach (var value in enumValues)
          editor.CreateEnumValue(value.Key, value.Value);
      }
    }

    [When("Редактор свойства. Кликнуть галочку (.*)")]
    public void WhenClickBooleanControl(string parameterName)
    {
      using (new TestLogs($"Редактор свойства. Кликнуть галочку {parameterName}", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        editor.IsRequired.Value.Click();
      }
    }

    [StepDefinition("Редактор свойства. Создать обработчик события (.*)")]
    public void CreateNewEvent(string eventName)
    {
      using (new TestLogs($"Редактор свойства. Создать обработчик события {eventName}", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var entityEvent = editor.CreateEventHandler(eventName);
        ScContext.Set(entityEvent);
      }
    }

    [StepDefinition("^Редактор свойства. Переместить значение перечисления (.*) (вверх|вниз)$")]
    public void WhenMoveEnumValue(string enumName, string direction)
    {
      using (new TestLogs($"Редактор свойства. Переместить значение перечисления {enumName} {direction}", true))
      {
        var changeEnumName = ScContext.Get<string>(Context.EnumName);
        var editor = ScContext.Get<EntityPropertyEditor>();

        var enumRowId = editor.EnumValue.Value.GetRowId(changeEnumName);
        ScContext.Add(Context.EnumRowId, enumRowId);

        if (direction.Equals("вверх"))
          editor.MoveEnumRowUp(changeEnumName);
        else
          editor.MoveEnumRowDown(changeEnumName);
      }
    }

    [Then("^Редактор свойства. В свойстве-перечислении (должны|не должно) быть значе(ния|ний)$")]
    public void ThenCheckEnumValueExists(string valueExist, string value)
    {
      using (new TestLogs($"Редактор свойства. В свойстве-перечислении {valueExist} быть значе{value}", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedEnumValues = ScContext.Get<Dictionary<string, string>>(Context.EnumValues);
        var enumValues = editor.EnumValue.Value.GetAllValues();

        var isExist = expectedEnumValues.All(v => enumValues.TryGetValue(v.Key, out var displayValue) && displayValue == v.Value);

        isExist.Should().Be(valueExist == "должны", $"В свойстве-перечислении {valueExist} быть значе{value}");
      }
    }

    [StepDefinition("Редактор свойства. Удалить значение перечисления")]
    public void WhenDeleteEnumValue()
    {
      using (new TestLogs("Редактор свойства. Удалить значение перечисления", true))
      {
        var enumValues = ScContext.Get<Dictionary<string, string>>(Context.EnumValues);
        var editor = ScContext.Get<EntityPropertyEditor>();
        foreach (var value in enumValues)
          editor.DeleteEnumRow(value.Key);
      }
    }

    [Then("Редактор свойства. Имя свойства должно быть изменено")]
    public void ThenCheckNameValue()
    {
      using (new TestLogs("Редактор свойства. Имя свойства должно быть изменено", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var changedPropertyName = ScContext.Get<string>(EntityPropertyEditorSteps.Context.ChangedPropertyValue);

        var propertyName = editor.SimpleParameters[EntityPropertyEditor.Name].Value.GetValue();

        propertyName.Should().BeEquivalentTo(changedPropertyName, "Имя свойства изменено.");
      }
    }

    [Then("Редактор свойства. Имя в базе данных свойства должно быть изменено")]
    public void ThenCheckCodeValue()
    {
      using (new TestLogs("Редактор свойства. Имя в базе данных свойства должно быть изменено", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedCode = ScContext.Get<string>(EntityPropertyEditorSteps.Context.ChangedPropertyValue);

        var code = editor.SimpleParameters[EntityPropertyEditor.Code].Value.GetValue();

        code.Should().BeEquivalentTo(expectedCode, "Имя в базе данных свойства изменено.");
      }
    }

    [Then("Редактор свойства. Отображаемое имя свойства должно быть изменено")]
    public void ThenCheckDisplayNameValue()
    {
      using (new TestLogs("Редактор свойства. Отображаемое имя свойства должно быть изменено", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedDisplayName = ScContext.Get<string>(EntityPropertyEditorSteps.Context.ChangedPropertyValue);

        var displayName = editor.SimpleParameters[EntityPropertyEditor.DisplayName].Value.GetValue();

        displayName.Should().BeEquivalentTo(expectedDisplayName, "Отображаемое имя свойства изменено.");
      }
    }

    [Then("Редактор свойства. Тип свойства должен быть изменен")]
    public void ThenCheckTypeValue()
    {
      using (new TestLogs("Редактор свойства. Тип свойства должен быть изменен", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedPropertyType = ScContext.Get<string>(Context.ChangedPropertyValue);

        var propertyType = editor.SimpleParameters[EntityPropertyEditor.PropertyType].Value.GetValue();

        propertyType.Should().BeEquivalentTo(expectedPropertyType, "Тип свойства изменен.");
      }
    }

    [Then("^Редактор свойства. Тип сущности должен быть (заполнен|изменен)$")]
    public void ThenCheckEntityTypeValue(string typeAction)
    {
      using (new TestLogs($"Редактор свойства. Тип сущности должен быть {typeAction}", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedEntityType = ScContext.Get<string>(Context.EntityFullName);

        var entityType = editor.SimpleParameters[EntityPropertyEditor.EntityType].Value.GetValue();

        entityType.Should().BeEquivalentTo(expectedEntityType, $"Тип сущности {typeAction}.");
      }
    }

    [Then("Редактор свойства. Обязательность свойства должна быть изменена")]
    public void ThenCheckIsRequiredValue()
    {
      using (new TestLogs("Редактор свойства. Обязательность свойства должна быть изменена", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedIsRequired = ScContext.Get<bool>(Context.ChangedPropertyValue);

        var IsRequired = editor.IsRequired.Value.IsChecked();

        IsRequired.Should().Be(expectedIsRequired, "Обязательность свойства изменена.");
      }
    }

    [Then("Редактор свойства. Отображаемое имя не должно быть пустым")]
    public void ThenCheckIsDisplayNameNotNull()
    {
      using (new TestLogs("Редактор свойства. Отображаемое имя не должно быть пустым", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var propertyDisplayName = editor.SimpleParameters[EntityPropertyEditor.DisplayName].Value.GetValue();

        propertyDisplayName.Should().NotBeNullOrEmpty("Отображаемое имя не пустое.");
      }
    }

    [Then("Редактор свойства. В контроле Имя должны отображаться сообщения валидации")]
    public void CheckValidationMessageInName()
    {
      using (new TestLogs("Редактор свойства. В контроле Имя должны отображаться сообщения валидации", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedValidationMessages = ScContext.Get<string[]>(Context.ValidationMessages);
        var validationMessage = editor.GetValidationErrorsInName();
        var isMessageInHint = validationMessage.SequenceEqual(expectedValidationMessages);
        isMessageInHint.Should().BeTrue("У контрола Имя есть сообщение валидации.");
      }
    }

    [Then("Редактор свойства. В контроле Значение перечисления должны отображаться сообщения валидации")]
    public void CheckValidationMessageInEnumValue()
    {
      using (new TestLogs("Редактор свойства. В контроле Значение перечисления должны отображаться сообщения валидации", true))
      {
        var editor = ScContext.Get<EntityPropertyEditor>();
        var expectedValidationMessages = ScContext.Get<string[]>(Context.ValidationMessages);
        var validationMessage = editor.GetValidationErrorsInEnumValue();
        var isMessageInHint = validationMessage.SequenceEqual(expectedValidationMessages);
        isMessageInHint.Should().BeTrue("У контрола Значение перечисления есть сообщение валидации.");
      }
    }

    [Then("Редактор свойства. Должна отображаться иконка наличия обработчика события (.*)")]
    public void CheckCreatedEvent(string eventName)
    {
      using (new TestLogs($"Редактор свойства. Должна отображаться иконка наличия обработчика события {eventName}", true))
      {
        var entityEvent = ScContext.Get<LinkToEventHandler>();
        entityEvent.IsHandlerCreated().Should().BeTrue($"Иконка наличия обработчика события {eventName} отображается.");
      }
    }

    [Then("Редактор свойства. Ссылка события (.*) должна быть кликабельной")]
    public void CheckHandlerLink(string eventName)
    {
      using (new TestLogs($"Редактор свойства. Ссылка события {eventName} должна быть кликабельной", true))
      {
        var entityEvent = ScContext.Get<LinkToEventHandler>();
        entityEvent.IsHandlerLinkClickable().Should().BeTrue($"Ссылка события {eventName} кликабельна.");
      }
    }

    [Then("^Редактор свойства. Порядок значений перечисления (должен|не должен) быть изменен$")]
    public void ThenCheckEnumValueOrder(string changed)
    {
      using (new TestLogs($"Редактор свойства. Порядок значений перечисления {changed} быть изменен", true))
      {
        var enumName = ScContext.Get<string>(Context.EnumName);
        var editor = ScContext.Get<EntityPropertyEditor>();

        var enumRowId = editor.EnumValue.Value.GetRowId(enumName);
        var previousRowId = ScContext.Get<int>(Context.EnumRowId);
        var isChanged = enumRowId.Equals(previousRowId);

        isChanged.Should().Be(changed == "не должен", $"Порядок значений перечисления {changed} быть изменен");
      }
    }

    #endregion

    #region Вложенные классы

    public class Context
    {
      public const string PropertyName = "entityPropertyEditorPropertyName";

      public const string EntityFullName = "entityPropertyEditorEntityFullName";

      public const string EnumValues = "entityPropertyEditorEnumValues";

      public const string EnumName = "entityPropertyEditorEnumName";

      public const string EnumRowId = "entityPropertyEditorEnumRowId";

      public const string ChangedPropertyValue = "entityPropertyEditorChangedPropertyValue";

      public const string ValidationMessages = "entityPropertyEditorValidationMessages";

      public const string PropertyType = "entityPropertyEditorPropertyType";
    }

    #endregion

    #region Конструктор

    public EntityPropertyEditorSteps(ScenarioContext scContext) : base(scContext)
    {
    }

    #endregion
  }
}
