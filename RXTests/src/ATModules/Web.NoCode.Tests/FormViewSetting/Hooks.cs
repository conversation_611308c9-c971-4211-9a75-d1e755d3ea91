using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using Autotest.Web.Utils.Selenium;
using Reqnroll;
using Sungero.AutoTestObjects.AT;
using Sungero.AutoTestObjects.AT.Autotest;
using Sungero.AutoTestObjects.Base;
using Sungero.AutoTestObjects.Content;
using Sungero.AutoTestObjects.Core;
using Sungero.AutoTestObjects.SpecflowSteps;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutotestObjects.Web;
using Sungero.AutotestObjects.Web.Content;
using Sungero.AutotestObjects.Web.Controls;
using Sungero.AutotestObjects.Web.Core;
using Sungero.AutotestObjects.Web.Core.FormView;
using Sungero.AutotestObjects.Web.Core.FormView.Steps;
using Sungero.AutotestObjects.Web.Core.NoCode;
using Sungero.AutotestObjects.Web.SpecflowSteps;
using Sungero.AutotestObjects.Web.WF.ProcessKind;
using Sungero.AutotestObjects.Web.WF.ProcessKind.Steps;
using Sungero.AutoTestObjects.WF;
using Sungero.AutoTestObjectsAT.Web;
using Sungero.AutoTestObjectsAT.Web.Autotest;
using static Sungero.AutotestObjects.Web.Dialogs.LocalizationDialog;

namespace Web.NoCode.Tests.FormViewSetting
{
  [Binding]
  class FormViewHooks : WebSteps
  {
    const string TestDataFolderName = "TestData";
    readonly string defaultEntityTypeInFormView = TaskWithAppliedBlock.EntityGuid.ToString();
    private FormViewLocalizationDoc document;
    private FormView formView;

    [BeforeScenario("BeforeCreateTestEntityFormView")]
    public void BeforeCreateTestEntityFormView()
    {
      this.CloseFormViews(defaultEntityTypeInFormView);

      using (new TestLogs("Создание тестовой сущности представления формы."))
      {
        var name = TestDataGenerator.GenerateString(15);
        formView = CreateFormView(name, defaultEntityTypeInFormView);

        var viewSettingsFileName =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, viewSettingsFileName);
        ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
        ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
      }
    }

    [BeforeScenario("BeforeControlDisplayNameLocalization")]
    public void BeforeControlDisplayNameLocalization()
    {
      var entityType = FormViewLocalizationDoc.Guid.ToString();
      this.CloseFormViews(entityType);

      var name = TestDataGenerator.GenerateString(10);
      var priority = 100;
      formView = CreateFormView(name, entityType, priority);

      using (IntegrationClientConfigurator.Setup(Constants.Administrator))
      {
        CreateFormViewLocalizationDoc();
      }

      ScContext[ExplorerSteps.Context.EntityType] = FormView.DisplayName;
      var newLocalizedName = TestDataGenerator.GenerateString(5);
      ScContext[EntitiesSteps.Context.PropertyValues] = new Dictionary<string, string>
      {
        { PropertyNames.Ru, newLocalizedName }
      };
      var viewSettingsFileName =
        Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewSettings.json");
      FormView.SetEntitySettingsJsonBySQL(formView.Id, viewSettingsFileName);
      ScContext[EntitiesSteps.Context.Value] = newLocalizedName;
      ScContext[EntitiesSteps.Context.Entity] = formView;
      ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
      ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
      ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
      ScContext[FormViewCardSteps.Context.ControlProperty] = FormViewEditorSideBar.LocalizedProperty.ControlDisplayName;
    }

    [BeforeScenario("BeforeControlPlaceholderLocalization")]
    public void BeforeControlPlaceholderLocalization()
    {
      var entityType = FormViewLocalizationDoc.Guid.ToString();
      this.CloseFormViews(entityType);

      var name = TestDataGenerator.GenerateString(10);
      var priority = 100;
      formView = CreateFormView(name, entityType, priority);

      using (IntegrationClientConfigurator.Setup(Constants.Administrator))
      {
        CreateFormViewLocalizationDoc();
      }

      ScContext[ExplorerSteps.Context.EntityType] = FormView.DisplayName;
      var newLocalizedPlaceholder = TestDataGenerator.GenerateString(5);
      ScContext[EntitiesSteps.Context.PropertyValues] = new Dictionary<string, string>
      {
        { PropertyNames.English, newLocalizedPlaceholder }
      };
      var viewSettingsFileName =
        Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewSettings.json");
      FormView.SetEntitySettingsJsonBySQL(formView.Id, viewSettingsFileName);
      ScContext[EntitiesSteps.Context.Value] = newLocalizedPlaceholder;
      ScContext[EntitiesSteps.Context.Entity] = formView;
      ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
      ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
      ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
      ScContext[FormViewCardSteps.Context.ControlProperty] = FormViewEditorSideBar.LocalizedProperty.ControlPlaceholder;
      ScContext[FormViewCardSteps.Context.ControlName] = "PlaceholderProperty";
    }

    [BeforeScenario("BeforeActionButtonLocalization")]
    public void BeforeActionButtonLocalization()
    {
      var entityType = FormViewLocalizationDoc.Guid.ToString();
      this.CloseFormViews(entityType);

      var name = TestDataGenerator.GenerateString(10);
      var priority = 100;
      formView = CreateFormView(name, entityType, priority);

      using (IntegrationClientConfigurator.Setup(Constants.Administrator))
      {
        CreateFormViewLocalizationDoc();
      }

      ScContext[ExplorerSteps.Context.EntityType] = FormView.DisplayName;
      var newActionName = TestDataGenerator.GenerateString(5);
      ScContext[EntitiesSteps.Context.PropertyValues] = new Dictionary<string, string>
      {
        { PropertyNames.English, newActionName }
      };
      var viewSettingsFileName =
        Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewSettings.json");
      FormView.SetEntitySettingsJsonBySQL(formView.Id, viewSettingsFileName);

      ScContext[EntitiesSteps.Context.Value] = newActionName;
      ScContext[EntitiesSteps.Context.Entity] = formView;
      ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
      ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
      ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
      ScContext[FormViewCardSteps.Context.ControlProperty] = FormViewEditorSideBar.LocalizedProperty.ControlDisplayName;
      ScContext[FormViewCardSteps.Context.ControlName] = FormViewLocalizationDocCard.LocalizedProperties.ButtonName;
    }

    [BeforeScenario("BeforeActionButtonDescriptionLocalization")]
    public void BeforeActionButtonDescriptionLocalization()
    {
      var entityType = FormViewLocalizationDoc.Guid.ToString();
      this.CloseFormViews(entityType);

      var name = TestDataGenerator.GenerateString(10);
      var priority = 100;
      formView = CreateFormView(name, entityType, priority);

      using (IntegrationClientConfigurator.Setup(Constants.Administrator))
      {
        CreateFormViewLocalizationDoc();
      }

      ScContext[ExplorerSteps.Context.EntityType] = FormView.DisplayName;
      var newActionDescription = TestDataGenerator.GenerateString(5);
      ScContext[EntitiesSteps.Context.PropertyValues] = new Dictionary<string, string>
      {
        { PropertyNames.English, newActionDescription }
      };
      var viewSettingsFileName =
        Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewSettings.json");
      FormView.SetEntitySettingsJsonBySQL(formView.Id, viewSettingsFileName);

      ScContext[EntitiesSteps.Context.Entity] = formView;
      ScContext[EntitiesSteps.Context.Value] = newActionDescription;
      ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
      ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
      ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
      ScContext[FormViewCardSteps.Context.ControlProperty] = FormViewEditorSideBar.LocalizedProperty.ControlDescription;
      ScContext[FormViewCardSteps.Context.ControlName] = FormViewLocalizationDocCard.LocalizedProperties.ButtonName;
    }

    [BeforeScenario("BeforeAddToContextExpressionTokens")]
    public void BeforeAddToContextExpressionTokens()
    {
      var containsOperatorToken = NoCodeModule.ExpressionOperators.Contains;
      ScContext.Add(ConditionsEditorSteps.Context.Operator, containsOperatorToken);

      var expression = new[]
        { FormView.Resources.CurrentUser, FormView.Resources.Department, FormView.Resources.AllUsers };
      ScContext.Add(ConditionsEditorSteps.Context.LeftExpression, expression);
    }

    [BeforeScenario("@BeforeСозданиеПредставленияКарточкиПоУмолчаниюДляБлокаЗаданиеВСхемеВариантаПроцесса")]
    public void BeforeСозданиеПредставленияПоУмолчаниюДляБлокаЗаданиеВСхемеВариантаПроцесса()
    {
      var entityType = SimpleAssignment.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестового варианта процесса с блоком Задание"))
      {
        var processKind = CreateProcessKind();
        var TaskSchemePath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "AssignmentBaseScheme.json");
        ProcessKind.SetSchemeBySQLWithReplaceUuid(processKind.Id, null, null, null, TaskSchemePath);

        ScContext.Add(BlockSettingsSteps.Context.BlockProperty,
          AssignmentBlockSettings.LocalizedProperties.FormViewDefault);
        ScContext.Add(CardPageSteps.Context.ModalCard, new FormViewCard(DisplayMode.Modal));
        ScContext.Add(EntitiesSteps.Context.Id, processKind.Id);
        ScContext.Add(EntitiesSteps.Context.Name, processKind.Name);
        ScContext.Add(EntitiesSteps.Context.Guid, ProcessKind.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new ProcessKindCard());
      }
    }

    [BeforeScenario("@BeforeСозданиеПредставленияКарточкиВСпискеДляБлокаУведомлениеВСхемеВариантаПроцесса")]
    public void BeforeСозданиеПредставленияКарточкиВСпискеДляБлокаУведомлениеВСхемеВариантаПроцесса()
    {
      var entityType = Notice.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестового варианта процесса с блоком Уведомление"))
      {
        var processKind = CreateProcessKind();
        var TaskSchemePath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "NoticeBaseScheme.json");
        ProcessKind.SetSchemeBySQL(processKind.Id, TaskSchemePath);

        ScContext.Add(BlockSettingsSteps.Context.BlockProperty,
          NoticeBlockSettings.LocalizedProperties.FormViewEmbedded);
        ScContext.Add(CardPageSteps.Context.ModalCard, new FormViewCard(DisplayMode.Modal));
        ScContext.Add(EntitiesSteps.Context.Id, processKind.Id);
        ScContext.Add(EntitiesSteps.Context.Name, processKind.Name);
        ScContext.Add(EntitiesSteps.Context.Guid, ProcessKind.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new ProcessKindCard());
      }
    }

    [BeforeScenario("@BeforeСозданиеПредставленияКарточкиПоУмолчаниюДляБлокаЗадачаВСхемеВариантаПроцесса")]
    public void BeforeСозданиеПредставленияКарточкиПоУмолчаниюДляБлокаЗадачаВСхемеВариантаПроцесса()
    {
      var entityType = SubTask.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестового варианта процесса с блоком Задача"))
      {
        var processKind = CreateProcessKind();
        var TaskSchemePath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "ModuleTaskScheme.json");
        ProcessKind.SetSchemeBySQL(processKind.Id, TaskSchemePath);

        ScContext.Add(BlockSettingsSteps.Context.BlockProperty, TaskBlockSettings.LocalizedProperties.FormViewDefault);
        ScContext.Add(CardPageSteps.Context.ModalCard, new FormViewCard(DisplayMode.Modal));
        ScContext.Add(EntitiesSteps.Context.Id, processKind.Id);
        ScContext.Add(EntitiesSteps.Context.Name, processKind.Name);
        ScContext.Add(EntitiesSteps.Context.Guid, ProcessKind.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new ProcessKindCard());
      }
    }

    [BeforeScenario("@BeforeВыборПредставленияКарточкиВСпискеДляБлокаЗаданиеВСхемеВариантаПроцесса")]
    public void BeforeВыборПредставленияКарточкиВСпискеДляБлокаЗаданиеВСхемеВариантаПроцесса()
    {
      var entityType = SimpleAssignment.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs(
               "Создание тестового варианта процесса с блоком Задание и представления формы для карточки в списке для блока"))
      {
        var processKind = CreateProcessKind();
        var TaskSchemePath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "AssignmentBaseScheme.json");
        ProcessKind.SetSchemeBySQLWithReplaceUuid(processKind.Id, null, null, null, TaskSchemePath);

        var formViewName = processKind.Name;
        var priority = 1;
        this.CreateFormView(formViewName, entityType, priority, true, "EmbeddedView");

        ScContext.Add(BlockSettingsSteps.Context.BlockProperty,
          AssignmentBlockSettings.LocalizedProperties.FormViewEmbedded);
        ScContext.Add(BlockSettingsSteps.Context.BlockPropertyValue, formViewName);
        ScContext.Add(EntitiesSteps.Context.Id, processKind.Id);
        ScContext.Add(EntitiesSteps.Context.Name, processKind.Name);
        ScContext.Add(EntitiesSteps.Context.Guid, ProcessKind.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new ProcessKindCard());
      }
    }

    [BeforeScenario("@BeforeВыборПредставленияКарточкиПоУмолчаниюДляБлокаУведомлениеВСхемеВариантаПроцесса")]
    public void BeforeВыборПредставленияКарточкиПоУмолчаниюДляБлокаУведомлениеВСхемеВариантаПроцесса()
    {
      var entityType = Notice.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестового варианта процесса с блоком Уведомление и представления формы для блока"))
      {
        var processKind = CreateProcessKind();
        var TaskSchemePath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "NoticeBaseScheme.json");
        ProcessKind.SetSchemeBySQL(processKind.Id, TaskSchemePath);

        var formViewName = processKind.Name;
        var priority = 1;
        this.CreateFormView(formViewName, entityType, priority, true);

        ScContext.Add(BlockSettingsSteps.Context.BlockProperty,
          NoticeBlockSettings.LocalizedProperties.FormViewDefault);
        ScContext.Add(BlockSettingsSteps.Context.BlockPropertyValue, formViewName);
        ScContext.Add(EntitiesSteps.Context.Id, processKind.Id);
        ScContext.Add(EntitiesSteps.Context.Name, processKind.Name);
        ScContext.Add(EntitiesSteps.Context.Guid, ProcessKind.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new ProcessKindCard());
      }
    }

    [BeforeScenario("@BeforeВыборПредставленияКарточкиВСпискеДляБлокаЗадачаВСхемеВариантаПроцесса")]
    public void BeforeВыборПредставленияКарточкиВСпискеДляБлокаЗадачаВСхемеВариантаПроцесса()
    {
      var entityType = SubTask.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs(
               "Создание тестового варианта процесса с блоком Задача и представления формы для карточки в списке для блока"))
      {
        var processKind = CreateProcessKind();
        var TaskSchemePath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "ModuleTaskScheme.json");
        ProcessKind.SetSchemeBySQL(processKind.Id, TaskSchemePath);

        var formViewName = processKind.Name;
        var priority = 1;
        this.CreateFormView(formViewName, entityType, priority, true, "EmbeddedView");

        ScContext.Add(BlockSettingsSteps.Context.BlockProperty, TaskBlockSettings.LocalizedProperties.FormViewEmbedded);
        ScContext.Add(BlockSettingsSteps.Context.BlockPropertyValue, formViewName);
        ScContext.Add(EntitiesSteps.Context.Id, processKind.Id);
        ScContext.Add(EntitiesSteps.Context.Name, processKind.Name);
        ScContext.Add(EntitiesSteps.Context.Guid, ProcessKind.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new ProcessKindCard());
      }
    }

    [BeforeScenario("@BeforeПоискВариантовПроцессаВБлокахКоторыхИспользуетсяПредставлениеФормыИзКарточкиПредставления")]
    public void BeforeПоискВариантовПроцессаВБлокахКоторыхИспользуетсяПредставлениеФормыИзКарточки()
    {
      var entityType = SimpleAssignment.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs(
               "Создание представления формы для блока и тестовых вариантов процесса (с использованием формы и без)"))
      {
        var name = TestDataGenerator.GenerateString(15);
        var priority = 1;
        var formView = CreateFormView(name, entityType, priority, true);
        var defaultProcessKind = CreateProcessKind();
        var foundProcessKind = CreateProcessKind();

        var TaskSchemePath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "AssignmentBaseScheme.json");
        ProcessKind.SetSchemeBySQLWithReplaceUuid(foundProcessKind.Id, formView.Uuid.ToString(), "", "",
          TaskSchemePath);

        ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(EntitiesSteps.Context.Guid, FormView.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
        ScContext.Add(EntitiesSteps.Context.Values, new List<string>() { foundProcessKind.Name });
        ScContext.Add(EntitiesSteps.Context.Value, defaultProcessKind.Name);
        ScContext.Add(EntitiesSteps.Context.Entities, new List<ProcessKind> { defaultProcessKind, foundProcessKind });
      }
    }

    [BeforeScenario("@BeforeОтсутствиеДействияИспользуетсяВВариантахПроцессаДляОбычныхПредставленийНаЛенте")]
    public void BeforeОтсутствиеДействияИспользуетсяВВариантахПроцессаДляОбычныхПредставленийНаЛенте()
    {
      this.CloseFormViews(defaultEntityTypeInFormView);

      using (new TestLogs("Создание тестового обычного представления формы"))
      {
        var name = TestDataGenerator.GenerateString(15);
        var formView = CreateFormView(name, defaultEntityTypeInFormView);
        var actions = new string[] { FormViewCard.ActionsId.FindUsages };

        ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
        ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, FormView.Guid);
        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(CardPageSteps.Context.Action, FormViewCard.ActionsId.FindUsages);
        ScContext.Add(CardPageSteps.Context.InvisibleActionsGuids, actions);
      }
    }

    [BeforeScenario("@BeforeОтображениеХинтаВКарточкеПредставленияФормыДляБлока")]
    public void BeforeОтображениеХинтаВКарточкеПредставленияФормыДляБлока()
    {
      this.CloseFormViews(defaultEntityTypeInFormView);

      using (new TestLogs("Создание тестового представления формы для блока"))
      {
        var name = TestDataGenerator.GenerateString(15);
        var priority = 1;
        var formView = CreateFormView(name, defaultEntityTypeInFormView, priority, true);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, FormView.Guid);
        ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
        ScContext.Add(CardPageSteps.Context.ValidationMessage, FormViewCard.HintUsedInBlock);
      }
    }

    [BeforeScenario("@BeforeСкрытиеКолонкиСвойстваКоллекцииВПредставленииФормыКарточкиДокумента")]
    public void BeforeСкрытиеКолонкиСвойстваКоллекцииВПредставленииФормыКарточкиДокумента()
    {
      var entityType = DocumentWithControlState.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестовой сущности представления формы"))
      {
        var name = TestDataGenerator.GenerateString(15);
        var formView = this.CreateFormView(name, entityType);
        var formPath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, formPath);
        var localizationPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridLocalization.json");
        FormView.SetLocalizationBySQL(formView.Uuid.ToString(), localizationPath);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
        ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
        ScContext.Add(ChildGridEditorModalSteps.Context.PropertyId, DocumentCard.PropertiesId.VersionsId);
        ScContext.Add(ChildGridEditorModalSteps.Context.ColumnName, DocumentVersion.LocalizedProperties.IsHidden);
        ScContext.Add(ChildGridEditorModalSteps.Context.ChildPropertyId, DocumentVersion.PropertiesId.IsHiddenId);
      }
    }

    [BeforeScenario("@BeforeСкрытиеДействияСвойстваКоллекцииВПредставленииФормыКарточкиДокумента")]
    public void BeforeСкрытиеДействияСвойстваКоллекцииВПредставленииФормыКарточкиДокумента()
    {
      var entityType = DocumentWithControlState.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестовой сущности представления формы"))
      {
        var name = TestDataGenerator.GenerateString(15);
        var formView = this.CreateFormView(name, entityType);
        var formPath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, formPath);
        var localizationPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridLocalization.json");
        FormView.SetLocalizationBySQL(formView.Uuid.ToString(), localizationPath);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(EntitiesSteps.Context.Id, formView.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, formView.EntityGuid);
        ScContext.Add(CardPageSteps.Context.Card, new FormViewCard());
        ScContext.Add(ChildGridEditorModalSteps.Context.PropertyId, DocumentCard.PropertiesId.VersionsId);
        ScContext.Add(ChildGridEditorModalSteps.Context.ActionId, DocumentCard.ActionsId.ShowVersionSignatures);
        ScContext.Add(ChildGridEditorModalSteps.Context.ActionGroupId, DocumentCard.ActionsGroupId.SignGroupId);
      }
    }

    [BeforeScenario("@BeforeСкрытиеКолонкиСвойстваКоллекцииВКарточкеДокументаНастроенногоВПредставленииФормыДокумента")]
    public void BeforeСкрытиеКолонкиСвойстваКоллекцииВКарточкеДокументаНастроенногоВПредставленииФормыДокумента()
    {
      var entityType = DocumentWithControlState.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестовой сущности представления формы и документа."))
      {
        var name = TestDataGenerator.GenerateString(15);
        var values = new Dictionary<string, object>
        {
          { BaseDocument.PropertyNames.Name, name }
        };
        DocumentWithControlState doc;
        using (IntegrationClientConfigurator.Setup(ConstantsATModules.ATModulesAdmin))
        {
          doc = Entity.Create<DocumentWithControlState>(values);
        }

        var docCard = new DocumentWithControlStateCard();

        var formView = this.CreateFormView(name, entityType);
        var formPath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, formPath);
        var localizationPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridLocalization.json");
        FormView.SetLocalizationBySQL(formView.Uuid.ToString(), localizationPath);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(EntitiesSteps.Context.Id, doc.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, Guid.Parse(entityType));
        ScContext.Add(CardPageSteps.Context.Card, docCard);
        ScContext.Add(ChildGridSteps.Context.ChildGrid,
          new ChildGridPage(docCard, BaseDocument.LocalizedProperties.Versions));
        ScContext.Add(ChildGridSteps.Context.ColumnName, DocumentVersion.LocalizedProperties.VersionNumber);
      }
    }

    [BeforeScenario(
      "@BeforeСкрытиеДействияСвойстваКоллекцииВКарточкеДокументаНастроенногоВПредставленииФормыДокумента")]
    public void BeforeСкрытиеДействияСвойстваКоллекцииВКарточкеДокументаНастроенногоВПредставленииФормыДокумента()
    {
      var entityType = DocumentWithControlState.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестовой сущности представления формы и документа."))
      {
        var name = TestDataGenerator.GenerateString(15);
        var values = new Dictionary<string, object>
        {
          { BaseDocument.PropertyNames.Name, name }
        };
        DocumentWithControlState doc;
        using (IntegrationClientConfigurator.Setup(ConstantsATModules.ATModulesAdmin))
        {
          doc = Entity.Create<DocumentWithControlState>(values);
        }

        var docCard = new DocumentWithControlStateCard();

        var formView = this.CreateFormView(name, entityType);
        var formPath = Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, formPath);
        var localizationPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridLocalization.json");
        FormView.SetLocalizationBySQL(formView.Uuid.ToString(), localizationPath);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(EntitiesSteps.Context.Id, doc.Id);
        ScContext.Add(EntitiesSteps.Context.Guid, Guid.Parse(entityType));
        ScContext.Add(CardPageSteps.Context.Card, docCard);
        ScContext.Add(ChildGridSteps.Context.ChildGrid,
          new ChildGridPage(docCard, BaseDocument.LocalizedProperties.Versions));
        ScContext.Add(ChildGridSteps.Context.Action, BaseDocument.Actions.Sign);
      }
    }

    [BeforeScenario(
      "@BeforeСкрытиеКолонкиСвойстваКоллекцииВКарточкеДокументаВСпискеНастроенногоВПредставленииФормыДокумента")]
    public void BeforeСкрытиеКолонкиСвойстваКоллекцииВКарточкеДокументаВСпискеНастроенногоВПредставленииФормыДокумента()
    {
      var entityType = DocumentWithControlState.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестовой сущности представления формы и документа."))
      {
        var name = TestDataGenerator.GenerateString(15);
        var values = new Dictionary<string, object>
        {
          { BaseDocument.PropertyNames.Name, name }
        };
        DocumentWithControlState doc;
        using (IntegrationClientConfigurator.Setup(ConstantsATModules.ATModulesAdmin))
        {
          doc = Entity.Create<DocumentWithControlState>(values);
        }

        var docCard = new DocumentWithControlStateCard();

        var priority = 1;
        var formView = this.CreateFormView(name, entityType, priority, false, FormView.CardViewTypeEnum.Embedded);
        var etalonFormPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, etalonFormPath);
        var localizationPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridLocalization.json");
        FormView.SetLocalizationBySQL(formView.Uuid.ToString(), localizationPath);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(GridPageSteps.Context.Grid, new DocumentWithControlStateGrid());
        ScContext.Add(ChildGridSteps.Context.ChildGrid,
          new ChildGridPage(docCard, BaseDocument.LocalizedProperties.Versions));
        ScContext.Add(ChildGridSteps.Context.ColumnName, DocumentVersion.LocalizedProperties.VersionNumber);
      }
    }

    [BeforeScenario(
      "@BeforeСкрытиеДействияСвойстваКоллекцииВКарточкеДокументаВСпискеНастроенногоВПредставленииФормыДокумента")]
    public void
      BeforeСкрытиеДействияСвойстваКоллекцииВКарточкеДокументаВСпискеНастроенногоВПредставленииФормыДокумента()
    {
      var entityType = DocumentWithControlState.EntityGuid.ToString();
      this.CloseFormViews(entityType);

      using (new TestLogs("Создание тестовой сущности представления формы и документа."))
      {
        var name = TestDataGenerator.GenerateString(15);
        var values = new Dictionary<string, object>
        {
          { BaseDocument.PropertyNames.Name, name }
        };
        DocumentWithControlState doc;
        using (IntegrationClientConfigurator.Setup(ConstantsATModules.ATModulesAdmin))
        {
          doc = Entity.Create<DocumentWithControlState>(values);
        }

        var docCard = new DocumentWithControlStateCard();

        var priority = 1;
        var formView = this.CreateFormView(name, entityType, priority, false, FormView.CardViewTypeEnum.Embedded);
        var etalonFormPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridSettings.json");
        FormView.SetEntitySettingsJsonBySQL(formView.Id, etalonFormPath);
        var localizationPath =
          Path.Combine(Environment.CurrentDirectory, TestDataFolderName, "FormViewGridLocalization.json");
        FormView.SetLocalizationBySQL(formView.Uuid.ToString(), localizationPath);

        ScContext.Add(EntitiesSteps.Context.Name, name);
        ScContext.Add(GridPageSteps.Context.Grid, new DocumentWithControlStateGrid());
        ScContext.Add(ChildGridSteps.Context.ChildGrid,
          new ChildGridPage(docCard, BaseDocument.LocalizedProperties.Versions));
        ScContext.Add(ChildGridSteps.Context.Action, BaseDocument.Actions.Sign);
      }
    }

    [StepDefinition("Help. Поменять контекст карточки FormViewCard на FormViewLocalizationDoc")]
    public void ChangeContextFormViewCard()
    {
      using (new TestLogs("Поменять контекст карточки FormViewCard на FormViewLocalizationDoc"))
      {
        ScContext[EntitiesSteps.Context.Guid] = document.EntityGuid;
        ScContext[CardPageSteps.Context.Card] = new FormViewLocalizationDocCard();
        ScContext[EntitiesSteps.Context.Id] = document.Id;
      }
    }

    [StepDefinition("Help. Поменять контекст карточки FormViewLocalizationDoc на FormViewCard")]
    public void ChangeContextFormViewLocalizationDoc()
    {
      using (new TestLogs("Поменять контекст карточки FormViewLocalizationDoc на FormViewCard"))
      {
        ScContext[EntitiesSteps.Context.Guid] = formView.EntityGuid;
        ScContext[CardPageSteps.Context.Card] = new FormViewCard();
        ScContext[EntitiesSteps.Context.Id] = formView.Id;
      }
    }

    [StepDefinition("Help. Поменять контекст с именами вариантов процесса")]
    public void ChangeContextValues()
    {
      using (new TestLogs("Поменять контекст с именами вариантов процесса"))
      {
        ScContext.Remove(EntitiesSteps.Context.Values);
        var unusedProcessKindName = ScContext.Get<string>(EntitiesSteps.Context.Value);
        ScContext.Add(EntitiesSteps.Context.Values, new List<string>() { unusedProcessKindName });
      }
    }

    [AfterScenario("@AfterСозданиеПредставленияКарточкиПоУмолчаниюДляБлокаЗаданиеВСхемеВариантаПроцесса")]
    [AfterScenario("@AfterСозданиеПредставленияКарточкиВСпискеДляБлокаУведомлениеВСхемеВариантаПроцесса")]
    [AfterScenario("@AfterСозданиеПредставленияКарточкиПоУмолчаниюДляБлокаЗадачаВСхемеВариантаПроцесса")]
    [AfterScenario("@AfterВыборПредставленияКарточкиВСпискеДляБлокаЗаданиеВСхемеВариантаПроцесса")]
    [AfterScenario("@AfterВыборПредставленияКарточкиПоУмолчаниюДляБлокаУведомлениеВСхемеВариантаПроцесса")]
    [AfterScenario("@AfterВыборПредставленияКарточкиВСпискеДляБлокаЗадачаВСхемеВариантаПроцесса")]
    public void AfterСозданиеИзБлокаПредставлений()
    {
      using (new TestLogs("Удаление тестовых варианта процесса и представления формы"))
      {
        CleanProcessKind();
        var processKindName = ScContext.Get<string>(EntitiesSteps.Context.Name);
        CleanFormViews($"contains(Name,'{processKindName}')");
      }
    }

    [AfterScenario("@AfterПоискВариантовПроцессаВБлокахКоторыхИспользуетсяПредставлениеФормыИзКарточкиПредставления")]
    public void AfterПоискВариантовПроцессаВБлокахКоторыхИспользуетсяПредставлениеФормыИзКарточкиПредставления()
    {
      using (new TestLogs("Удаление тестовых вариантов процесса и представления формы"))
      {
        var processKinds = ScContext.Get<List<ProcessKind>>(EntitiesSteps.Context.Entities);
        processKinds.ForEach(pk => pk.Delete());
        var processKindName = ScContext.Get<string>(EntitiesSteps.Context.Name);
        CleanFormViews($"contains(Name,'{processKindName}')");
      }
    }

    [AfterScenario("@AfterОтсутствиеДействияИспользуетсяВВариантахПроцессаДляОбычныхПредставленийНаЛенте")]
    [AfterScenario("@AfterОтображениеХинтаВКарточкеПредставленияФормыДляБлока")]
    [AfterScenario("@AfterСкрытиеКолонкиСвойстваКоллекцииВПредставленииФормыКарточкиДокумента")]
    [AfterScenario("@AfterСкрытиеДействияСвойстваКоллекцииВПредставленииФормыКарточкиДокумента")]
    public void AfterВыполнениеДействияИспользуетсяВВариантахПроцессаДляПредставленияБлокаСЛенты()
    {
      using (new TestLogs("Удаление тестового представления формы"))
      {
        var name = ScContext.Get<string>(EntitiesSteps.Context.Name);
        CleanFormViews($"contains(Name,'{name}')");
      }
    }

    [AfterScenario("@AfterСкрытиеКолонкиСвойстваКоллекцииВКарточкеДокументаНастроенногоВПредставленииФормыДокумента")]
    [AfterScenario("@AfterСкрытиеДействияСвойстваКоллекцииВКарточкеДокументаНастроенногоВПредставленииФормыДокумента")]
    [AfterScenario(
      "@AfterСкрытиеКолонкиСвойстваКоллекцииВКарточкеДокументаВСпискеНастроенногоВПредставленииФормыДокумента")]
    [AfterScenario(
      "@AfterСкрытиеДействияСвойстваКоллекцииВКарточкеДокументаВСпискеНастроенногоВПредставленииФормыДокумента")]
    public void AfterОтображениеВКарточкеДокументаСвойстваКоллекцииНастроенногоВПредставленииФормыДокумента()
    {
      using (new TestLogs("Удаление тестового представления формы и документа"))
      {
        var entityType = DocumentWithControlState.EntityGuid.ToString();
        var cardInListSetting = LocalStorage.GetKeys()
          .FirstOrDefault(s => s.Contains($"Local/1/viewSettings/List_Layout_{entityType}"));
        if (cardInListSetting != null)
          LocalStorage.RemoveItem(cardInListSetting);

        var name = ScContext.Get<string>(EntitiesSteps.Context.Name);
        CleanFormViews($"contains(Name,'{name}')");
        var doc = Entity.Get<DocumentWithControlState>($"Name eq '{name}'").FirstOrDefault();
        doc.Delete();
      }
    }

    [AfterScenario("AfterCleanUpFormView", Order = 1)]
    public void CleanUpFormView()
    {
      using (new TestLogs("Очистка тестовых данных для представления формы."))
      {
        CleanFormViews($"{FormView.PropertyNames.EntityType} eq '{defaultEntityTypeInFormView}' " +
                       $"AND {Entity.PropertyNames.Id} eq {formView.Id}");
      }
    }

    [AfterScenario("AfterControlLocalization", Order = 1)]
    public void AfterControlDisplayNameLocalization()
    {
      var entity = ScContext.Get<FormView>(EntitiesSteps.Context.Entity);
      entity.Delete();

      using (IntegrationClientConfigurator.Setup(Constants.Administrator))
      {
        document.Delete();
      }
    }

    [AfterScenario("AfterScenarioLocalization", Order = 0)]
    public void AfterScenarioLocalization()
    {
      CardPage docCard = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
      docCard.CloseCard();

      ChangeContextFormViewLocalizationDoc();

      CardPage formviewCard = ScContext.Get<CardPage>(CardPageSteps.Context.Card);
      formviewCard.CloseCard();
    }

    public void CreateFormViewLocalizationDoc()
    {
      var docValues = new Dictionary<string, object>
      {
        { BaseDocument.PropertyNames.Name, $"LocalizationTestDocument{TestDataGenerator.GenerateString(5)}" }
      };
      document = Entity.Create<FormViewLocalizationDoc>(docValues);
    }

    private ProcessKind CreateProcessKind()
    {
      using (new TestLogs("Создание тестового варианта процесса"))
      {
        var name = TestDataGenerator.GenerateString(15);
        var values = new Dictionary<string, object>
        {
          { Databookentry.PropertyNames.Name, name },
          { ProcessKind.PropertyNames.Description, name },
          { ProcessKind.PropertyNames.Priority, int.MaxValue },
          { ProcessKind.PropertyNames.TaskType, TaskWithAppliedBlock.EntityGuid.ToString() },
          { ProcessKind.PropertyNames.Uuid, Guid.NewGuid().ToString() },
          { Databookentry.PropertyNames.Status, Databookentry.StatusEnum.Active }
        };
        ProcessKind processKind;
        using (IntegrationClientConfigurator.Setup(Constants.Administrator))
        {
          processKind = Entity.Create<ProcessKind>(values);
        }

        return processKind;
      }
    }

    private FormView CreateFormView(string name, string entityType, int priority = 1, bool isAutogenerated = false,
      string cardViewType = "DefaultView")
    {
      using (new TestLogs("Создание тестовой сущности представления формы."))
      {
        var values = new Dictionary<string, object>
        {
          { Databookentry.PropertyNames.Name, name },
          { FormView.PropertyNames.EntityType, entityType },
          { NoCodeEntity.PropertyNames.Uuid, Guid.NewGuid().ToString() },
          { FormView.PropertyNames.Priority, priority },
          { FormView.PropertyNames.CardViewType, cardViewType },
          { FormView.PropertyNames.IsAutogenerated, isAutogenerated }
        };

        using (IntegrationClientConfigurator.Setup(Constants.Administrator))
        {
          formView = Entity.Create<FormView>(values);
        }

        return formView;
      }
    }

    private void CleanProcessKind()
    {
      var processKindId = ScContext.Get<long>(EntitiesSteps.Context.Id);
      var processKind = Entity.GetById<ProcessKind>(processKindId);
      processKind.Delete();
    }

    private void CleanFormViews(string filter)
    {
      var formViews = Entity.Get<FormView>(filter);
      foreach (var formView in formViews)
      {
        formView.TryUnLock();
        formView.Delete();
      }
    }

    private void CloseFormViews(string entityType)
    {
      var filter = $"EntityType eq '{entityType}' and Status eq 'Active'";
      var viewForms = Entity.Get<FormView>(filter).ToList();
      foreach (var viewForm in viewForms)
      {
        viewForm.TryUnLock();
        viewForm.SetStatus(viewForm.Collection, Databookentry.StatusEnum.Closed);
      }
    }

    public FormViewHooks(ScenarioContext scContext) : base(scContext)
    {
    }
  }
}