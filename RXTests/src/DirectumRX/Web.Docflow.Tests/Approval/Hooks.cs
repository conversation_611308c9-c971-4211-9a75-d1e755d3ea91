using System.Collections.Generic;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using Reqnroll;
using Sungero.AutoTestObjects.Base;
using Sungero.AutoTestObjects.SpecflowSteps;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutotestObjects.Web;
using Sungero.AutotestObjects.Web.SpecflowSteps;
using Sungero.AutoTestObjectsRX.Docflow;
using Sungero.AutoTestObjectsRX.Web.Docflow;
using Constants = Sungero.AutoTestObjectsRX.Constants;

namespace Web.Docflow.Tests.Approval
{
  [Binding]
  public class Hooks : WebSteps
  {
    [BeforeScenario("BeforeСозданиеЭтапаСогласования")]
    public void SetUp()
    {
      var grid = new ApprovalStageGrid();
      var card = new ApprovalStageCard();
      var approvalStageParams = new Dictionary<string, string>
      {
        { ApprovalStage.PropertyNames.Name, TestDataGenerator.GenerateString(10) },
        { ApprovalStage.PropertyNames.DeadlineInDays, "1" }
      };

      ScContext.Add(CardPageSteps.Context.Card, card);
      ScContext.Add(GridPageSteps.Context.Grid, grid);
      ScContext.Add(GridPageSteps.Context.Action, ApprovalStage.DisplayName);
      ScContext.Add(CardPageSteps.Context.CardGroup, ApprovalStage.Resources.ReworkGroup);
      ScContext.Add(CardPageSteps.Context.PropertyValues, approvalStageParams);
    }

    [AfterScenario("AfterСозданиеЭтапаСогласования")]
    public void AfterCreateApprovalStage()
    {
      using (new TestLogs("Удалить этап согласования"))
      {
        using (IntegrationClientConfigurator.Setup(Constants.RXAdminLogin))
        {
          var approvalStageId = ScContext.Get<long>(EntitiesSteps.Context.Id);
          var stage = Entity.GetById<ApprovalStage>(approvalStageId);

          if (stage == null) return;

          stage.Delete();
        }
      }
    }

    public Hooks(ScenarioContext scContext) : base(scContext)
    {
    }
  }
}