using System.Collections.Generic;
using System.Linq;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using Reqnroll;
using Sungero.AutoTestObjects.Base;
using Sungero.AutoTestObjects.Core;
using Sungero.AutoTestObjects.SpecflowSteps;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutotestObjects.Web;
using Sungero.AutotestObjects.Web.Core;
using Sungero.AutotestObjects.Web.Dialogs;
using Sungero.AutotestObjects.Web.SpecflowSteps;
using Sungero.AutoTestObjectsRX.Company;
using Sungero.AutoTestObjectsRX.Parties;
using Sungero.AutoTestObjectsRX.Web.Company;
using Sungero.AutoTestObjectsRX.Web.Company.SpecflowSteps;
using Sungero.AutoTestObjectsRX.Web.Parties;
using Constants = Sungero.AutoTestObjectsRX.Constants;

namespace Web.CommonRX.Tests.Hooks
{
  [Binding]
  public class Hooks : WebSteps
  {
    private string companyTin = "7714698320";

    private string Document = Sungero.AutoTestObjectsRX.Docflow.DocumentKind.Entities.Document;

    private string WayBill = Sungero.AutoTestObjectsRX.Docflow.DocumentKind.Entities.Waybill;

    private string Contract = Sungero.AutoTestObjectsRX.Docflow.DocumentKind.Entities.Contract;

    [BeforeScenario("BeforeСозданиеНашейОрганизацииСНевалиднымИНН")]
    private void BeforeCreateWithInvalidINN()
    {
      var companyGrid = new CompanyGrid();
      var businessUnitCard = new BusinessUnitCard();
      var email = TestDataGenerator.Faker.Internet.Email(provider: "yandex.ru");
      var properties = new Dictionary<string, string>
      {
        { BusinessUnit.PropertyNames.Name, TestDataGenerator.Faker.Company.CompanyName(0) },
        { BusinessUnit.PropertyNames.HeadCompany, BusinessUnit.Entities.TehSystem },
        { BusinessUnit.PropertyNames.Email, email }
      };
      var localizedProperties = new List<string>
      {
        BusinessUnit.PropertyNames.Nonresident,
        BusinessUnit.PropertyNames.Nonresident
      };

      ScContext.Add(CardPageSteps.Context.Card, businessUnitCard);
      ScContext.Add(GridPageSteps.Context.Grid, companyGrid);
      ScContext.Add(CardPageSteps.Context.PropertyValues, properties);
      ScContext.Add(CardPageSteps.Context.Properties, localizedProperties);
      ScContext.Add(DepartmentSteps.Context.Employee, Constants.ClerkUser);
      ScContext.Add(EntitiesSteps.Context.Value, properties[BusinessUnit.PropertyNames.Name]);
      ScContext.Add(EntitiesSteps.Context.Column, Company.LocalizedProperties.Name);
      ScContext.Add(CardPageSteps.Context.Property, Company.LocalizedProperties.Name);
    }

    [BeforeScenario("BeforeСозданиеБанка")]
    public void SetUp()
    {
      var bankCard = new BankCard();
      var properties = new Dictionary<string, string>
      {
        { Databookentry.PropertyNames.Name, TestDataGenerator.GenerateString(4) },
        { Counterparty.PropertyNames.Email, "<EMAIL>" }
      };
      var localizedProperties = new List<string>
      {
        Counterparty.PropertyNames.Nonresident,
        Counterparty.PropertyNames.Nonresident
      };

      ScContext.Add(CardPageSteps.Context.Card, bankCard);
      ScContext.Add(CardPageSteps.Context.PropertyValues, properties);
      ScContext.Add(DepartmentSteps.Context.Employee, Constants.ClerkUser);
      ScContext.Add(EntitiesSteps.Context.Value, properties[Databookentry.PropertyNames.Name]);
      ScContext.Add(EntitiesSteps.Context.Column, CompanyBase.LocalizedProperties.Name);
      ScContext.Add(CardPageSteps.Context.Property, CompanyBase.LocalizedProperties.Name);
      ScContext.Add(CardPageSteps.Context.Properties, localizedProperties);
    }

    [AfterScenario("AfterСозданиеБанка")]
    private void AfterCreateBankCloseDialogAndCard()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        var bankCard = ScContext.Get<BankCard>(CardPageSteps.Context.Card);
        var errorDialog = new TaskDialog(TaskDialog.DialogTypes.Error);
        if (errorDialog.Exists())
          errorDialog.CloseModalDialog();

        if (bankCard.CardExists())
          bankCard.CloseCard(false);
      }
    }

    [BeforeScenario("BeforeСозданиеПодразделенияНашейОрганизации")]
    public void CreateDepartmentSetUp()
    {
      var departmentCard = new DepartmentCard();
      var departmentName = TestDataGenerator.Faker.Commerce.Department();
      ScContext.Add(CardPageSteps.Context.Card, departmentCard);
      ScContext.Add(DepartmentSteps.Context.DepartmentName, departmentName);

      var properties = new Dictionary<string, string>
      {
        { Databookentry.PropertyNames.Name, departmentName },
        { Department.PropertyNames.BusinessUnit, BusinessUnit.Entities.TehSystem }
      };

      ScContext.Add(CardPageSteps.Context.PropertyValues, properties);
      ScContext.Add(DepartmentSteps.Context.Employee, Constants.ClerkUser);
    }

    [AfterScenario("AfterСозданиеПодразделенияНашейОрганизации")]
    public void CreateDepartmentCleanUp()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        using (IntegrationClientConfigurator.Setup(Constants.RXAdminLogin))
        {
          var departmentName = ScContext.Get<string>(DepartmentSteps.Context.DepartmentName);
          var department = Department.GetDepartment(departmentName);

          if (department == null)
            return;

          department.ClearRecipientLinks();
          Department.DeleteDepartment(department.Id);
        }
      }
    }

    [AfterScenario("AfterCloseDialogAndCard")]
    public void CreateDepartmentCloseDialogAndCard()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        var errorDialog = new TaskDialog(TaskDialog.DialogTypes.Error);
        if (errorDialog.Exists())
          errorDialog.CloseModalDialog();

        var departmentCard = ScContext.Get<DepartmentCard>(CardPageSteps.Context.Card);

        if (departmentCard.CardExists())
          departmentCard.CloseCard(false);
      }
    }

    [BeforeScenario("BeforeСозданиеНашейОрганизации")]
    public void CreateOurCompanySetUp()
    {
      var companyGrid = new CompanyGrid();
      var businessUnitCard = new BusinessUnitCard();
      var email = TestDataGenerator.Faker.Internet.Email(provider: "yandex.ru");
      var properties = new Dictionary<string, string>
      {
        { BusinessUnit.PropertyNames.Name, TestDataGenerator.Faker.Company.CompanyName(0) },
        { BusinessUnit.PropertyNames.HeadCompany, BusinessUnit.Entities.TehSystem },
        { BusinessUnit.PropertyNames.TIN, companyTin },
        { BusinessUnit.PropertyNames.Email, email }
      };

      ScContext.Add(CardPageSteps.Context.Card, businessUnitCard);
      ScContext.Add(GridPageSteps.Context.Grid, companyGrid);
      ScContext.Add(CardPageSteps.Context.PropertyValues, properties);
      ScContext.Add(DepartmentSteps.Context.Employee, Constants.ClerkUser);
      ScContext.Add(EntitiesSteps.Context.Value, properties[BusinessUnit.PropertyNames.Name]);
      ScContext.Add(EntitiesSteps.Context.Column, Company.LocalizedProperties.Name);
      ScContext.Add(CardPageSteps.Context.Property, Databookentry.PropertyNames.Name);
      ScContext.Add(BusinessUnitSteps.Context.Email, email);
    }

    [AfterScenario("AfterСозданиеНашейОрганизации")]
    public void CreateOurCompanyCleanUp()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        var businessUnitName = ScContext.Get<string>(EntitiesSteps.Context.Value);
        var filter = $"{BusinessUnit.PropertyNames.TIN} eq '{companyTin}' " +
                     $"AND {BusinessUnit.PropertyNames.Name} eq '{businessUnitName}'";
        using (IntegrationClientConfigurator.Setup(Constants.RXAdminLogin))
        {
          var ourCompanies = Entity.GetWithAllProperties<BusinessUnit>(filter);
          if (ourCompanies == null)
            return;

          foreach (var businessUnit in ourCompanies)
            businessUnit.Delete();
        }
      }
    }

    [AfterScenario("AfterСозданиеНашейОрганизацииСНевалиднымКодом")]
    private void CreateOurCompanyWithInvalideCodeCloseDialogAndCard()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        var errorDialog = new TaskDialog(TaskDialog.DialogTypes.Error);
        if (errorDialog.Exists())
          errorDialog.CloseModalDialog();

        var businessUnitCard = ScContext.Get<BusinessUnitCard>(CardPageSteps.Context.Card);

        if (businessUnitCard.CardExists())
          businessUnitCard.CloseCard(false);
      }
    }

    [BeforeScenario("BeforeСозданиеТипаСвязи")]
    public void CreateRelationTypeSetUp()
    {
      var relationTypeCardPage = new RelationTypeCard();
      var properties = new Dictionary<string, string>
      {
        { RelationTypeUI.LocalizedProperties.SourceColumnName, this.Document },
        { RelationTypeUI.LocalizedProperties.TargetColumnName, this.WayBill },
        { RelationTypeUI.LocalizedProperties.FillInPropertyColumnName, this.Contract },
      };

      ScContext.Add(CardPageSteps.Context.Card, relationTypeCardPage);
      ScContext.Add(ChildGridSteps.Context.PropertyValues, properties);
    }

    [AfterScenario("AfterСозданиеТипаСвязи")]
    public void CreateRelationTypeCleanUp()
    {
      var relationTypeCardPage = ScContext.Get<RelationTypeCard>(CardPageSteps.Context.Card);
      relationTypeCardPage?.CloseCard(false);
    }

    [BeforeScenario("BeforeИзменениеCвойствКарточкиОрганизации")]
    public void ChangeCompanyCardPropertiesSetUp()
    {
      var propertyValues = new Dictionary<string, string>
      {
        { Databookentry.PropertyNames.Name, TestDataGenerator.Faker.Company.CompanyName(0) },
        { CompanyBase.PropertyNames.TRRC, TestDataGenerator.Faker.Random.String2(9, "0123456789") },
        { Counterparty.PropertyNames.LegalAddress, TestDataGenerator.Faker.Address.FullAddress() }
      };
      var companyCard = new CompanyCard();
      var companyGrid = new CompanyGrid();

      ScContext.Add(GridPageSteps.Context.Grid, companyGrid);
      ScContext.Add(CardPageSteps.Context.Card, companyCard);
      ScContext.Add(CardPageSteps.Context.PropertyValues, propertyValues);
    }

    [AfterScenario("AfterИзменениеCвойствКарточкиОрганизации")]
    public void TearDown()
    {
      using (new TestLogs("Закрыть карточку организации", true))
      {
        var companyCard = ScContext.Get<CompanyCard>(CardPageSteps.Context.Card);
        companyCard.CloseCard();

        var company = ScContext.Get<Company>(EntitiesSteps.Context.Entity);
        company.Delete();
      }
    }

    [BeforeScenario("BeforeСозданиеCотрудникаНашейОрганизации")]
    public void CreateEmployeeOurCompanySetUp()
    {
      Person person;
      var employeeCard = new EmployeeCard();
      var firstName = TestDataGenerator.Faker.Name.FirstName();
      var lastName = TestDataGenerator.Faker.Name.LastName();
      using (IntegrationClientConfigurator.Setup(Constants.RXAdminLogin))
        person = Person.CreatePerson(firstName, lastName);

      var email = TestDataGenerator.Faker.Internet.Email(firstName: firstName,
        lastName: lastName, provider: "yandex.ru");
      var properties = new Dictionary<string, string>
      {
        { Employee.PropertyNames.Person, person.Name },
        { Employee.PropertyNames.Email, email }
      };
      var message = new List<string>
      {
        string.Format(Entity.Resources.RequiredPropertyIsNotFilled, Employee.LocalizedProperties.Department)
      };
      ScContext.Add(CardPageSteps.Context.Card, employeeCard);
      ScContext.Add(EmployeeSteps.Context.Person, person);
      ScContext.Add(CardPageSteps.Context.PropertyValues, properties);
      ScContext.Add(CardPageSteps.Context.ValidationMessage, message);
      ScContext.Add(EmployeeSteps.Context.Department, Department.Entities.FinancialDepartment);
    }

    [AfterScenario("AfterСозданиеCотрудникаНашейОрганизации")]
    public void CreateEmployeeCleanUp()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        var person = ScContext.Get<Person>(EmployeeSteps.Context.Person);
        var filter = $"Person/Id eq {person.Id}";
        using (IntegrationClientConfigurator.Setup(Constants.RXAdminLogin))
        {
          var employee = Entity.GetWithAllProperties<Employee>(filter).SingleOrDefault();
          if (employee == null) return;
          employee.Delete();
          person.Delete();
        }
      }
    }

    [AfterScenario("AfterСозданиеНашейОрганизацииСНевалиднымИНН")]
    private void CloseDialogAndCard()
    {
      using (new Logs(LoggerArea.CleanUp))
      {
        var businessUnitCard = ScContext.Get<BusinessUnitCard>(CardPageSteps.Context.Card);
        var errorDialog = new TaskDialog(TaskDialog.DialogTypes.Error);
        if (errorDialog.Exists())
          errorDialog.CloseModalDialog();

        if (businessUnitCard.CardExists())
          businessUnitCard.CloseCard(false);
      }
    }

    public Hooks(ScenarioContext scContext) : base(scContext)
    {
    }
  }
}