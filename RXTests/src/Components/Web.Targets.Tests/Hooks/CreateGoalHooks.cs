using System.Collections.Generic;
using Autotest.Base.Utils;
using Autotest.Web.Utils;
using Reqnroll;
using Sungero.AutoTestObjects.Base;
using Sungero.AutoTestObjects.SpecflowSteps;
using Sungero.AutoTestObjects.Targets.DTCommons;
using Sungero.AutoTestObjects.Targets.Targets;
using Sungero.AutoTestObjects.Targets.Targets.Steps;
using Sungero.AutoTestObjects.Targets.Web.Cards;
using Sungero.AutoTestObjects.Targets.Web.Grids;
using Sungero.AutoTestObjects.Targets.Web.Steps;
using Sungero.AutoTestObjects.Utils;
using Sungero.AutotestObjects.Web;
using Sungero.AutotestObjects.Web.SpecflowSteps;
using Sungero.AutoTestObjectsRX.Company;
using Constants = Sungero.AutoTestObjectsRX.Constants;

namespace Web.Targets.Tests.Hooks
{
  [Binding]
  public class CreateGoalHooks : WebSteps
  {
    #region Before

    [BeforeScenario("BeforeСозданиеОсновнойЦелиВГрафическомРедакторе")]
    private void BeforeСозданиеОсновнойЦелиВГрафическомРедакторе()
    {
      using (new TestLogs("Подготовка к тесту СозданиеОсновнойЦелиВГрафическомРедакторе"))
      using (IntegrationClientConfigurator.Setup(Constants.TechnicalEmployeeLogin))
      {
        var periodLabel = string.Format(TargetsMap.PeriodLabelValues.HalfYear,
          Calendar.GetHalfYearFromDate(Calendar.Now),
          Calendar.Today.Year);
        var targetsMap =
          TargetsMap.Create(periodLabel, Period.Periods.HalfYear, Calendar.Now);
        var targetMapCard = new TargetsMapCard();
        var targetName = TestDataGenerator.Faker.Lorem.Sentence(3);
        var targetGrid = new TargetGrid();

        ScContext[EntitiesSteps.Context.Id] = targetsMap.Id;
        ScContext[EntitiesSteps.Context.Guid] = targetsMap.EntityGuid;
        ScContext[CardPageSteps.Context.Card] = targetMapCard;
        ScContext[CardPageSteps.Context.FilledPropertyValues] = new Dictionary<string, string>
        {
          { Target.PropertyNames.StructuralUnit, Constants.TechnicalEmployee.Department.Name },
          { Target.PropertyNames.Period, periodLabel },
        };
        ScContext[GoalInputSteps.Context.GoalName] = targetName;
        ScContext[GridPageSteps.Context.Grid] = targetGrid;
        ScContext[EntitiesSteps.Context.Column] = targetGrid.DefaultPropertyName;
        ScContext[EntitiesSteps.Context.Values] = new List<string> { targetName };
      }
    }

    [BeforeScenario("BeforeСозданиеДочернейЦелиВГрафическомРедакторе")]
    private void BeforeСозданиеДочернейЦелиВГрафическомРедакторе()
    {
      using (new TestLogs("Подготовка к тесту СозданиеДочернейЦелиВГрафическомРедакторе"))
      using (IntegrationClientConfigurator.Setup(Constants.TechnicalEmployeeLogin))
      {
        var periodLabel = Calendar.Today.Year.ToString();
        var targetsMap =
          TargetsMap.Create(periodLabel, Period.Periods.Year, Calendar.Now);
        var department = Databookentry.GetByName<Department>(Department.Entities.TechDepartment);
        var leadingTargetParams = new Dictionary<string, object>
        {
          { Databookentry.PropertyNames.Name, TestDataGenerator.Faker.Lorem.Sentence(3) },
          { Target.PropertyNames.StructuralUnit, department },
          { Target.PropertyNames.PeriodLabel, periodLabel },
          { Target.PropertyNames.Period, Period.Periods.Year },
          { Target.PropertyNames.PeriodStart, Calendar.Now.ToOdata() }
        };
        var leadingTarget = Target.Create(leadingTargetParams);
        targetsMap.Save();
        leadingTarget = leadingTarget.Update<Target>();
        var targetGrid = new TargetGrid();
        var childTargetName = TestDataGenerator.Faker.Lorem.Sentence(3);

        ScContext[EntitiesSteps.Context.Id] = targetsMap.Id;
        ScContext[EntitiesSteps.Context.Guid] = targetsMap.EntityGuid;
        ScContext[CardPageSteps.Context.Card] = new TargetsMapCard();
        ScContext[CardPageSteps.Context.FilledPropertyValues] = new Dictionary<string, string>
        {
          { Target.PropertyNames.StructuralUnit, leadingTarget.StructuralUnit.Name },
          { Target.PropertyNames.Period, leadingTarget.PeriodLabel },
        };
        ScContext[TargetSteps.Context.LeadingTargetName] = leadingTarget.Name;
        ScContext[GridPageSteps.Context.Grid] = targetGrid;
        ScContext[EntitiesSteps.Context.Column] = targetGrid.DefaultPropertyName;
        ScContext[EntitiesSteps.Context.Values] = new List<string> { childTargetName };
        ScContext[GoalInputSteps.Context.GoalName] = ScContext[TargetSteps.Context.ChildTargetName] = childTargetName;
      }
    }

    #endregion

    #region After

    [AfterScenario("AfterСозданиеОсновнойЦелиВГрафическомРедакторе", Order = 0)]
    [AfterScenario("AfterСозданиеДочернейЦелиВГрафическомРедакторе", Order = 0)]
    public void AfterСозданиеОсновнойЦелиВГрафическомРедакторе()
    {
      using (new TestLogs("Очистка карт целей после теста"))
      using (IntegrationClientConfigurator.Setup(Constants.RXAdminLogin))
      {
        var targetsMaps = Entity.GetWithAllProperties<TargetsMap>();

        foreach (var targetsMap in targetsMaps)
        {
          targetsMap.TryUnLock();
          targetsMap.Delete();
        }
      }
    }

    #endregion

    public CreateGoalHooks(ScenarioContext scContext) : base(scContext)
    {
    }
  }
}