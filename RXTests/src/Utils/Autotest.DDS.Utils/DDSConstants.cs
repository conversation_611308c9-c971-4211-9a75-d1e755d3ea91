namespace Autotest.DDS.Utils
{
  public class DDSConstants
  {
    public const string CompanyCode = "Sungero";

    public static string DirectumLauncherPath = @"C:\SungeroScripts";

    public static string DevelopmentStudioDesktopRelativePath = @"etc\_builds_bin\DevelopmentStudioDesktop";

    public static string WorkRepository = @"C:\Projects\DDS";

    public static string BaseRepository = @"C:\Projects\ATModules";

    public static string RepositoryForConflictPath = @"C:\ProjectsForConflict\DDSTest";

    public static string RepositoryPath;

    public static string ConfigurationsValue;

    public static string IDEMockScriptFileName = "IDEMock.bat";

    public static string IDEMockCallParametersFileName = "IDEMockCallParameters.txt";

    public static string IconFileName = "Icon.svg";

    public class SolutionName
    {
      public const string SDSTests = "Sungero.SDSTests";

      public const string DeleteTest = "Sungero.DeleteTest";

      public const string OverrideTest = "Sungero.OverrideTest";
    }

    public class ModuleName
    {
      public const string ChangeModule = "ChangeModule";

      public const string DeleteModule = "DeleteModule";

      public const string EeTestModule = "EeTestModule";

      public const string DSTestModule = "DSTestModule";

      public const string MeetingModule = "MeetingModule";

      public const string ContentModule = "Content";

      public const string ReportingModule = "Reporting";

      public const string CoreEntitiesModule = "CoreEntities";
    }

    public class EntityName
    {
      public const string TestDocument = "TestDocument";

      public const string TestDatabook = "TestDatabook";

      public const string TestBook = "TestBook";

      public const string TestDatabookBase = "TestDatabookBase";

      public const string TestAssignment = "TestAssignment";

      public const string TestNotice = "TestNotice";

      public const string TestTask = "TestTask";

      public const string TestReviewAssignment = "TestReviewAssignment";

      public const string TestAssignmentTask = "TestAssignmentTask";

      public const string DSDatabook = "DSDatabook";

      public const string DSDocument = "DSDocument";

      public const string DSTask = "DSTask";

      public const string DSAssignment = "DSAssignment";

      public const string DSNotice = "DSNotice";

      public const string DSReviewAssignmentDSTask = "DSReviewAssignmentDSTask";

      public const string DSAssignmentDSTask = "DSAssignmentDSTask";

      public const string DSNoticeDSTask = "DSNoticeDSTask";

      public const string User = "User";

      public const string MeetingUser = "MeetingUser";
    }

    public class TestEntityIds
    {
      public const string OverridenNodeId = "4015f9ab-dbae-4d58-8d31-421ae60234ac";

      public const string InjectionsNodeId = "1bb8bb6e-79ae-4a10-ab91-756508a88d14";

      // Решения

      public const string SDSTestsSolutionId = "83b52d19-c027-4170-95b5-307857f703b7";

      public const string OverrideTestSolutionId = "cafdca4a-412d-496b-bc36-8042b713d698";

      // Модули

      public const string EeTestModuleId = "6132da9a-9d22-419d-bd0d-d75e3db3121b";

      public const string DSTestModuleId = "3e80f80f-efff-4340-b9b7-38e2481841af";

      public const string ChangeModuleId = "03cb8192-3628-4aaa-87b7-ef58a748d9e3";

      public const string ContentModuleId = "ec7b606a-21ee-4f16-aba8-ab8c2af76d12";

      // Сущности 

      public const string TestTaskEntityId = "e74e9765-902e-49cf-a59f-d08d3c6d7259";

      public const string TestAssignmentTaskEntityId = "8788ffd6-40d5-436c-80b6-fbaacb061414";

      public const string TestReviewAssignmentEntityId = "dbd30a47-0966-4334-8d2e-acef067b093b";

      public const string TestNoticeTaskEntityId = "c484876f-484d-4b29-9ebf-811c8f4d3206";

      public const string TestAssignmentEntityId = "94034107-57cc-49e5-8f12-34293274ee2a";

      public const string TestBookEntityId = "6784502d-84fb-41c0-b2fd-1e98a7a3f398";

      public const string TestDocumentEntityId = "ab521671-71bc-4627-9990-72a358037f55";

      public const string TestNoticeEntityId = "44f02a31-e9f0-4c0c-bcd2-2c2a81aa7406";

      public const string TestDatabookEntityId = "441ad39b-dda2-4a43-8ef7-ea84b910f749";

      public const string TestDatabookBaseEntityId = "316cddf9-0365-454c-ab37-73e442735eaa";

      public const string DSDocumentEntityId = "2c8e7418-daac-4ed7-af12-97e767fcb2d9";

      public const string DSDocumentBaseEntityId = "f71445fc-5c5b-4c18-bb93-7a10feef2746";

      public const string DSDatabookEntityId = "6b7c7770-6acf-4655-a313-f48694a23111";

      public const string DSTaskEntityId = "3e5a5c4e-f03c-4622-8812-64872b044a04";

      public const string DSAssignmentDSTaskEntityId = "8b4c5df6-0d15-40cc-9a98-c1f2be3bbed7";

      public const string DSReviewAssignmentDSTaskEntityId = "757a7a40-184c-49ed-9708-841c4b52daf4";

      public const string DSNoticeDSTaskEntityId = "48cc99af-eac2-46fd-935a-55ba9ac9c640";

      public const string DSAssignmentEntityId = "c8bbc7f3-e75d-4f45-8dc6-e9caa863dc63";

      public const string DSNoticeEntityId = "e70bf0bf-3f8d-4f6a-b14e-82699f6cb768";

      public const string TestTaskChangeModuleEntityId = "7b0e03a7-46e7-49f4-b63c-0c25a06af786";

      public const string UserEntityId = "243c2d26-f5f7-495f-9faf-951d91215c77";

      public const string MeetingUserEntityId = "b461084c-3d12-4308-8d3a-74ac5991ade5";

      // Коллекции 

      public const string DataBookCollectionId = "da4d3674-aec7-4fc5-8086-561accb148ec";

      public const string TestTaskCollectionPropertyId = "117cfe2f-870f-4e16-8b8d-aa0db8ed3e03";

      public const string TestCollectionId = "6a0da47f-ea7d-4729-ba23-92e7314737c9";
    }

    public class DevelopmentItemTypes
    {
      public const string Solution = "Solution";

      public const string Module = "Module";

      public const string Databook = "Databook";

      public const string Document = "Document";

      public const string Task = "Task";

      public const string Assignment = "Assignment";

      public const string Notice = "Notice";

      public const string ReviewAssignment = "ReviewAssignment";
    }

    public class MetadataItemTypes
    {
      public const string DateRange = "DateRange";

      public const string FlagList = "List";

      public const string Navigation = "Navigation";

      public const string Property = "Property";

      public const string CollectionProperty = "CollectionProperty";

      public const string ChildProperty = "ChildProperty";

      public const string Action = "Action";

      public const string AssignmentBlock = "Assignment";

      public const string ScriptBlock = "Script";

      public const string MonitoringBlock = "Monitoring";

      public const string NoticeBlock = "Notice";

      public const string TaskBlock = "Task";
    }

    public class LocalStorageKeys
    {
      public const string ActiveConfiguration = "ds-active-configuration";

      public const string Configurations = "ds-configurations";

      public const string IDE = "ds-ide";

      public const string SolutionTreesSplitter = "ds-solution-trees-splitter";

      public const string Theme = "ds-theme";
    }

    public const string TestBook = "TestBook";

    public static readonly string[] SolutionFileNames =
    {
      // Часть файлов зачем-то создается для решения по аналогии с модулем, и никак не используется.
      @"{0}\{0}.Server\ModuleServerFunctions.cs",
      @"{0}\{0}.Server\ModuleWidgetHandlers.cs",
      @"{0}\{0}.Server\ModuleDesktopViewHandlers.cs",
      @"{0}\{0}.Server\ModuleJobs.cs",
      @"{0}\{0}.Server\ModuleHandlers.cs",
      @"{0}\{0}.Server\ModuleQueries.xml",
      @"{0}\{0}.ClientBase\ModuleClientFunctions.cs",
      @"{0}\{0}.ClientBase\ModuleWidgetHandlers.cs",
      @"{0}\{0}.Shared\ModuleSystem.resx",
      @"{0}\{0}.Shared\ModuleSystem.ru.resx",
      @"{0}\{0}.Shared\ModuleStructures.cs",
      @"{0}\{0}.Shared\ModuleConstants.cs",
      @"{0}\{0}.Shared\ModuleSharedFunctions.cs",
      @"{0}\{0}.Shared\Module.mtd",
      @"{0}\{0}.Shared\Module.ru.resx",
      @"{0}\{0}.Shared\Module.resx",
      @"{0}\{0}.Settings\Module.json"
    };

    public static readonly string[] ModuleFileNames =
    {
      @"{0}\{0}.Server\ModuleServerFunctions.cs",
      @"{0}\{0}.Server\ModuleWidgetHandlers.cs",
      @"{0}\{0}.Server\ModuleInitializer.cs",
      @"{0}\{0}.Server\ModuleJobs.cs",
      @"{0}\{0}.Server\ModuleHandlers.cs",
      @"{0}\{0}.Server\ModuleQueries.xml",
      @"{0}\{0}.ClientBase\ModuleClientFunctions.cs",
      @"{0}\{0}.ClientBase\ModuleWidgetHandlers.cs",
      @"{0}\{0}.Shared\ModuleSystem.resx",
      @"{0}\{0}.Shared\ModuleSystem.ru.resx",
      @"{0}\{0}.Shared\ModuleStructures.cs",
      @"{0}\{0}.Shared\ModuleConstants.cs",
      @"{0}\{0}.Shared\ModuleSharedFunctions.cs",
      @"{0}\{0}.Shared\Module.mtd",
      @"{0}\{0}.Shared\Module.ru.resx",
      @"{0}\{0}.Shared\Module.resx",
      @"{0}\{0}.Settings\Module.json"
    };

    public static readonly string[] EntityTypeFileNames =
    {
      @"{0}\{0}.ClientBase\{1}\{1}Handlers.cs",
      @"{0}\{0}.ClientBase\{1}\{1}Actions.cs",
      @"{0}\{0}.ClientBase\{1}\{1}ClientFunctions.cs",
      @"{0}\{0}.Server\{1}\{1}Handlers.cs",
      @"{0}\{0}.Server\{1}\{1}ServerFunctions.cs",
      @"{0}\{0}.Shared\{1}\{1}.mtd",
      @"{0}\{0}.Shared\{1}\{1}Constants.cs",
      @"{0}\{0}.Shared\{1}\{1}Structures.cs",
      @"{0}\{0}.Shared\{1}\{1}Handlers.cs",
      @"{0}\{0}.Shared\{1}\{1}SharedFunctions.cs",
      @"{0}\{0}.Shared\{1}\{1}System.resx",
      @"{0}\{0}.Shared\{1}\{1}System.ru.resx",
      @"{0}\{0}.Shared\{1}\{1}.ru.resx",
      @"{0}\{0}.Shared\{1}\{1}.resx"
    };

    public static readonly string[] TaskTypeFileNames =
{
      @"{0}\{0}.ClientBase\{1}\{1}Handlers.cs",
      @"{0}\{0}.ClientBase\{1}\{1}Actions.cs",
      @"{0}\{0}.ClientBase\{1}\{1}ClientFunctions.cs",
      @"{0}\{0}.Server\{1}\{1}Handlers.cs",
      @"{0}\{0}.Server\{1}\{1}ServerFunctions.cs",
      @"{0}\{0}.Shared\{1}\{1}.mtd",
      @"{0}\{0}.Shared\{1}\{1}Constants.cs",
      @"{0}\{0}.Shared\{1}\{1}Structures.cs",
      @"{0}\{0}.Shared\{1}\{1}Handlers.cs",
      @"{0}\{0}.Shared\{1}\{1}SharedFunctions.cs",
      @"{0}\{0}.Shared\{1}\{1}System.resx",
      @"{0}\{0}.Shared\{1}\{1}System.ru.resx",
      @"{0}\{0}.Shared\{1}\{1}.ru.resx",
      @"{0}\{0}.Shared\{1}\{1}.resx",
      @"{0}\{0}.Server\{1}\{1}RouteHandlers.cs",
      @"{0}\{0}.Server\{1}\{1}Scheme.xml",
    };

    public static readonly string[] ReportsFileNames =
    {
      @"{0}\{0}.Server\Reports\{1}\{1}.g.cs",
      @"{0}\{0}.Server\Reports\{1}\{1}.frx",
      @"{0}\{0}.Server\Reports\{1}\{1}Handlers.cs",
      @"{0}\{0}.Server\Reports\{1}\{1}Queries.xml",
      @"{0}\{0}.ClientBase\Reports\{1}\{1}.g.cs",
      @"{0}\{0}.ClientBase\Reports\{1}\{1}Handlers.cs",
      @"{0}\{0}.Shared\Reports\{1}\{1}.g.cs",
      @"{0}\{0}.Shared\Reports\{1}\{1}System.resx",
      @"{0}\{0}.Shared\Reports\{1}\{1}System.ru.resx",
      @"{0}\{0}.Shared\Reports\{1}\{1}Structures.cs",
      @"{0}\{0}.Shared\Reports\{1}\{1}Structures.g.cs",
      @"{0}\{0}.Shared\Reports\{1}\{1}Constants.cs",
      @"{0}\{0}.Shared\Reports\{1}\{1}.mtd",
      @"{0}\{0}.Shared\Reports\{1}\{1}.ru.resx",
      @"{0}\{0}.Shared\Reports\{1}\{1}.resx"
    };

    public static readonly string[] OverrideModuleFileNames =
    {
      @"{0}\{1}\{1}.Server\{2}\Module.g.cs",
      @"{0}\{1}\{1}.Server\{2}\ModuleServerFunctions.cs",
      @"{0}\{1}\{1}.Server\{2}\ModuleWidgetHandlers.cs",
      @"{0}\{1}\{1}.Server\{2}\ModuleJobs.cs",
      @"{0}\{1}\{1}.Server\{2}\ModuleHandlers.cs",
      @"{0}\{1}\{1}.Server\{2}\ModuleQueries.xml",
      @"{0}\{1}\{1}.ClientBase\{2}\Module.g.cs",
      @"{0}\{1}\{1}.ClientBase\{2}\ModuleClientFunctions.cs",
      @"{0}\{1}\{1}.ClientBase\{2}\ModuleWidgetHandlers.cs",
      @"{0}\{1}\{1}.Shared\{2}\Module.g.cs",
      @"{0}\{1}\{1}.Shared\{2}\ModuleSystem.resx",
      @"{0}\{1}\{1}.Shared\{2}\ModuleSystem.ru.resx",
      @"{0}\{1}\{1}.Shared\{2}\ModuleStructures.cs",
      @"{0}\{1}\{1}.Shared\{2}\ModuleStructures.g.cs",
      @"{0}\{1}\{1}.Shared\{2}\ModuleConstants.cs",
      @"{0}\{1}\{1}.Shared\{2}\ModuleSharedFunctions.cs",
      @"{0}\{1}\{1}.Shared\{2}\Module.mtd",
      @"{0}\{1}\{1}.Shared\{2}\Module.ru.resx",
      @"{0}\{1}\{1}.Shared\{2}\Module.resx"
    };

    public class ToolBarButtons
    {
      public const string Add = "Создать";

      public const string CancelChangesButtonName = "Reset";

      public const string Databook = "Справочник";

      public const string Document = "Документ";

      public const string Module = "Модуль";

      public const string Report = "Отчет";

      public const string Solution = "Решение";

      public const string Task = "Задача";

      public const string Notice = "Уведомление";

      public const string Assignment = "Задание";

      public const string AssignmentReviewName = "Задание на приемку";
    }

    public class ContextMenuButtons
    {
      public const string Delete = "Удалить";

      public const string OpenSystemResources = "Открыть системные ресурсы";

      public const string OpenResources = "Открыть ресурсы";

      public const string Override = "Перекрыть";
    }

    public class EditorsNames
    {
      public const string Module = "Модуль";
    }

    public class Renames
    {
      public const string ModuleName = "RenameModule";
    }

    public class EventNamesGenerator
    {
      public class ClientEvents
      {
        public const string ValueInput = "ValueInput";

        public const string Filtering = "Filtering";

        public const string ExecuteActon = "Action";
      }

      public class ServerEvents
      {
        public const string PropertyFiltering = "PropertyFiltering";

        public const string Filtering = "Filtering";

        public const string GetValue = "Value";

        public const string CanApplyDesktopView = "CanApply";
      }

      public class SharedEvents
      {
        public const string Changed = "Changed";

        public const string Added = "Added";

        public const string Deleted = "Deleted";
      }

      public static string GetPropertyEventName(string eventName, string propertyName)
      {
        return $"{propertyName}{eventName}";
      }

      public static string GetDesktopEventName(string eventName, string propertyName)
      {
        return $"{eventName}{propertyName}";
      }

      public static string GetFolderEventsClassName(string folderName)
      {
        return $"{folderName}FolderHandlers";
      }

      public static string GetWidgetEventName(string eventName, string widgetName, string actionName)
      {
        return $"{widgetName}{actionName}{eventName}";
      }

      public static string GetWidgetEventsClass(string widgetName)
      {
        return $"{widgetName}WidgetHandlers";
      }

      public static string GetArgumentPropertyEventName(string entityName, string propertyName, string eventName)
      {
        return $"{entityName}{propertyName}{eventName}EventArgs";
      }

      public static string GetArgumentChildPropertyEventName(string entityName, string collectionProperty, string propertyName, string eventName)
      {
        return $"{entityName}{collectionProperty}{propertyName}{eventName}EventArgs";
      }
    }
  }
}
