using System;
using Autotest.Base.Utils;

namespace Autotest.Web.Utils
{
  public class WebGuiException : AutoTestException
  {
    protected sealed override bool DoScrin => true;

    public WebGuiException(string messge) : base(messge)
    {
      TestLogs.Info($"{messge}", DoScrin);
    }

    public WebGuiException(string messge, params object[] parms) : base(messge, parms)
    {
      var mess = string.Format(messge, parms);
      TestLogs.Info($"{mess}", DoScrin);
    }

    public WebGuiException(string messge, Exception innerExc) : base(messge, innerExc)
    {
      var mess = $"{messge} {innerExc.Message}";
      TestLogs.Info($"{messge}", DoScrin);
    }

    public WebGuiException(string messge, Exception innerExc, params object[] parms) : base(messge, innerExc, parms)
    {
      var mess = string.Format(messge, parms);
      TestLogs.Info($"{mess}", DoScrin);
    }
  }
}